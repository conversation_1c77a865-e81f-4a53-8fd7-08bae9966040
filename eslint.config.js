import { base, react, storybook, nextjs } from '@cleevio/eslint';

export default [
  ...base,
  ...react,
  ...storybook,
  ...nextjs(),
  {
    ignores: ['.next', 'coverage', 'next-end.d.ts', 'public/', 'src/lib/api.ts', '.yarn', 'playwright-report'],
  },
  {
    files: ['src/**/*'],
    rules: {
      '@typescript-eslint/no-misused-promises': 'off',
    },
  },
  {
    files: ['src/assets/**/*.tsx'],
    rules: {
      'no-magic-numbers': 'off',
      'react/jsx-props-no-spreading': 'off',
    },
  },
  {
    files: ['src/**/*.tsx'],
    rules: {
      'react/jsx-props-no-spreading': 'off',
    },
  },
  {
    files: ['src/**/*.test.tsx', 'src/**/*.test.ts', 'vitest.setup.ts', 'src/providers.tsx', 'react-scan'],
    rules: {
      'import/no-extraneous-dependencies': 'off',
    },
  },
  {
    files: ['./src/**/utils.ts', './src/**/utils.tsx', './src/**/*-utils.ts', 'src/**/utils.test.ts'],
    rules: {
      'unicorn/prevent-abbreviations': 'off',
    },
  },
  {
    files: ['./src/**/layout.tsx', './src/**/page.tsx', './src/**/*.action.ts'],
    rules: {
      '@typescript-eslint/require-await': 'off',
    },
  },
  {
    files: ['src/**/*.tsx'],
    rules: {
      'no-restricted-imports': [
        'error',
        {
          paths: [
            {
              name: 'sonner',
              message: 'Do not import from sonner directly. Use custom toast implementation at @/components/toast.',
            },
          ],
        },
      ],
    },
  },
];
