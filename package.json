{"name": "fatbot", "version": "1.2.2", "private": true, "type": "module", "scripts": {"analyze": "ANALYZE=true yarn build", "build": "next build", "build-storybook": "storybook build", "clean": "del .eslintcache tsconfig.tsbuildinfo tsconfig.build.tsbuildinfo .next node_modules", "codegen": "orval --config orval.config.cjs", "dev": "next dev --turbopack", "dev:host": "next dev --experimental-https -H fatbot.devel.cleevio.dev -p 443", "format": "prettier --check \"./**/*.{js,json,md,ts}\"", "format:fix": "yarn run format --write", "lint": "eslint --cache .", "lint:fix": "yarn run lint --fix", "lint:next": "next lint", "lint:next:fix": "next lint --fix", "prepare": "husky", "start": "next start", "storybook": "storybook dev -p 6006", "tc": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "test": "vitest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui --debug", "ts": "tsc --build --noEmit tsconfig.json"}, "resolutions": {"@types/react": "19.1.6", "@types/react-dom": "19.1.5"}, "overrides": {"@opentelemetry/instrumentation": "0.56.0"}, "dependencies": {"@cloudflare/stream-react": "^1.9.3", "@firebase/app": "^0.10.18", "@firebase/app-check": "^0.8.13", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.2", "@react-spring/web": "^10.0.1", "@sentry/nextjs": "^9.17.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.56.2", "@tanstack/react-virtual": "^3.10.9", "@visx/group": "^3.12.0", "@visx/responsive": "^3.12.0", "@visx/scale": "^3.12.0", "@visx/shape": "^3.12.0", "@visx/text": "^3.12.0", "axios": "^1.7.7", "bignumber.js": "^9.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "core-js": "^3.41.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.5.2", "emoji-regex": "^10.4.0", "eventemitter3": "^5.0.1", "firebase": "^10.13.2", "firebase-admin": "^12.6.0", "framer-motion": "^12.10.5", "husky": "^9.1.7", "input-otp": "^1.4.2", "lightweight-charts": "^4.2.3", "lint-staged": "^16.1.0", "lottie-react": "^2.4.1", "lucide-react": "^0.446.0", "modern-screenshot": "^4.6.4", "motion": "^11.11.17", "next": "^15.3.3", "next-firebase-auth-edge": "^1.8.2", "next-intl": "^4.1.0", "next-themes": "^0.3.0", "node-cache": "^5.1.2", "nuqs": "^2.1.1", "orval": "7.5.0", "ramda": "^0.30.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.53.1", "react-number-format": "^5.4.3", "react-qr-code": "^2.0.15", "react-stomp-hooks": "^3.0.1", "server-only": "^0.0.1", "sharp": "^0.33.5", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "ts-pattern": "^5.7.0", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "viem": "^2.21.54", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0-next.16", "@cleevio/eslint": "^0.1.1", "@cleevio/lint-staged": "^0.1.0", "@cleevio/prettier": "^0.1.0", "@cleevio/tsconfig": "^0.2.0", "@commitlint/cli": "^19.7.1", "@next/bundle-analyzer": "15.3.3", "@playwright/test": "^1.52.0", "@storybook/addon-docs": "^9.1.0-alpha.2", "@storybook/addon-links": "^9.1.0-alpha.2", "@storybook/addon-onboarding": "^9.1.0-alpha.2", "@storybook/addon-themes": "^9.1.0-alpha.2", "@storybook/nextjs": "^9.1.0-alpha.2", "@svgr/webpack": "^8.1.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/postcss": "^4.0.17", "@tanstack/react-query-devtools": "^5.61.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/lodash.get": "^4", "@types/lodash.set": "^4", "@types/node": "^22.15.21", "@types/node-cache": "^4.2.5", "@types/ramda": "^0.30.2", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "@vitejs/plugin-react": "^4.5.0", "chalk": "^5.4.1", "csv": "^6.3.11", "del-cli": "^6.0.0", "enquirer": "^2.4.1", "eslint": "^9.27.0", "execa": "^9.6.0", "jsdom": "^26.1.0", "lodash.get": "^4.4.2", "lodash.set": "^4.3.2", "postcss": "^8.4.47", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react-scan": "^0.3.3", "storybook": "^9.1.0-alpha.2", "storybook-next-intl": "^2.0.8", "tailwindcss": "^4.0.17", "tsx": "^4.20.3", "typescript": "~5.8.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4"}, "packageManager": "yarn@4.5.0", "engines": {"node": ">=22.15"}}