

> fatbot@1.2.3 format:fix /Users/<USER>/Workspace/fatbot/apps/fatbot
> pnpm run format --write


> fatbot@1.2.3 format /Users/<USER>/Workspace/fatbot/apps/fatbot
> prettier --check "./**/*.{js,json,md,ts}" --write

Checking formatting...
.storybook/main.ts[2K[1G.storybook/next-intl.ts[2K[1Gcomponents.json[2K[1Glint-staged.config.js[2K[1Gmessages/en.json[2K[1Gnext-env.d.ts[2K[1Gnext.config.ts[2K[1Gpackage.json[2K[1Gplaywright.config.ts[2K[1Gpostcss.config.js[2K[1Gsentry.client.config.ts[2K[1Gsentry.edge.config.ts[2K[1Gsentry.server.config.ts[2K[1Gsrc/api/actions.ts[2K[1Gsrc/api/constants.ts[2K[1Gsrc/api/index.ts[2K[1Gsrc/api/mocks/bot-drafts.json[2K[1Gsrc/api/mocks/last-transactions.json[2K[1Gsrc/api/mocks/my-tokens.json[2K[1Gsrc/api/mocks/token-price-area.json[2K[1Gsrc/api/mocks/user-streak.ts[2K[1Gsrc/api/typeguards/is-token-dexresult.ts[2K[1Gsrc/app/(app)/bot-trading/(detail)/detail/[botId]/layout.ts[2K[1Gsrc/app/(public)/auth/logout/page.ts[2K[1Gsrc/app/(public)/verify-email/handle-apply-action-code.ts[2K[1Gsrc/app/fonts/index.ts[2K[1Gsrc/assets/brand-icons/index.ts[2K[1Gsrc/assets/chart-icons/index.ts[2K[1Gsrc/assets/icons/index.ts[2K[1Gsrc/assets/images/fatty-heads/index.ts[2K[1Gsrc/assets/images/index.ts[2K[1Gsrc/assets/images/onboarding/index.ts[2K[1Gsrc/assets/index.ts[2K[1Gsrc/assets/league/index.ts[2K[1Gsrc/assets/league/streak-rewards/index.ts[2K[1Gsrc/assets/trend-down.ts[2K[1Gsrc/components/animate-number.ts[2K[1Gsrc/components/animations/bot-status-active/bot-status-active-lottie.json[2K[1Gsrc/components/animations/bot-status-deactived/bot-status-deactivated-lottie.json[2K[1Gsrc/components/animations/bot-status-limited/bot-status-limited-lottie.json[2K[1Gsrc/components/animations/bot-status-no-balance/bot-status-no-balance-lottie.json[2K[1Gsrc/components/animations/claim-success/successfully-claimed_230.json[2K[1Gsrc/components/animations/find/find_230.json[2K[1Gsrc/components/animations/fireworks-animation-2/fireworks_B_230.json[2K[1Gsrc/components/animations/fireworks-animation/fireworks_A_460.json[2K[1Gsrc/components/animations/onboarding-step-1/onboarding_230.json[2K[1Gsrc/components/animations/onboarding-step-2/manual-trading_230.json[2K[1Gsrc/components/animations/onboarding-step-3/automated-bots_230.json[2K[1Gsrc/components/animations/purchase-success/purchase-success_230.json[2K[1Gsrc/components/animations/signup/purchase-success_230.json[2K[1Gsrc/components/animations/signup/signup_230.json[2K[1Gsrc/components/animations/trade-validation/securing-sale_230.json[2K[1Gsrc/components/animations/wallet-success/wallet-success_230.json[2K[1Gsrc/components/animations/withdraw-success/successfully-withdrawn_230.json[2K[1Gsrc/components/charts/area-chart/types.ts[2K[1Gsrc/components/charts/area-chart/use-options.ts[2K[1Gsrc/components/charts/area-chart/utils.test.ts[2K[1Gsrc/components/charts/area-chart/utils.ts[2K[1Gsrc/components/charts/constants.ts[2K[1Gsrc/components/charts/donut-chart/constants.ts[2K[1Gsrc/components/charts/donut-chart/utils.ts[2K[1Gsrc/components/charts/slider-chart/utils.ts[2K[1Gsrc/components/charts/trading-view/add-trade-points-to-chart.ts[2K[1Gsrc/components/charts/trading-view/format-bar-data.ts[2K[1Gsrc/components/charts/trading-view/utils.ts[2K[1Gsrc/components/charts/use-token-chart-controls.ts[2K[1Gsrc/components/charts/use-token-chart-store.ts[2K[1Gsrc/components/charts/utils/format-area-data.ts[2K[1Gsrc/components/charts/utils/format-token-chart-data.ts[2K[1Gsrc/components/charts/utils/time-tick-formatter.ts[2K[1Gsrc/components/charts/utils/timestamp-formatter.ts[2K[1Gsrc/components/react-scan.ts[2K[1Gsrc/components/relative-date.ts[2K[1Gsrc/components/slider/types.ts[2K[1Gsrc/components/slider/utils.ts[2K[1Gsrc/components/ui/display.ts[2K[1Gsrc/constants/config.ts[2K[1Gsrc/constants/constants.ts[2K[1Gsrc/constants/external-links.ts[2K[1Gsrc/constants/index.ts[2K[1Gsrc/constants/profile-links.ts[2K[1Gsrc/constants/remote-config.ts[2K[1Gsrc/constants/routes.ts[2K[1Gsrc/constants/sentry.ts[2K[1Gsrc/env/client.ts[2K[1Gsrc/env/server.ts[2K[1Gsrc/env/validator.test.ts[2K[1Gsrc/env/validator.ts[2K[1Gsrc/firebase.ts[2K[1Gsrc/hooks/use-ephemeral-value.ts[2K[1Gsrc/hooks/use-firebase-error-message.ts[2K[1Gsrc/hooks/use-previous-route-track.ts[2K[1Gsrc/hooks/use-relative-date-format.ts[2K[1Gsrc/hooks/use-remaining-time.ts[2K[1Gsrc/hooks/use-sorted-wallets.test.ts[2K[1Gsrc/hooks/use-sorted-wallets.ts[2K[1Gsrc/instrumentation.ts[2K[1Gsrc/lib/addresses-equals.test.ts[2K[1Gsrc/lib/addresses-equals.ts[2K[1Gsrc/lib/api.ts[2K[1Gsrc/lib/axios-instance.ts[2K[1Gsrc/lib/calculate-ratio.test.ts[2K[1Gsrc/lib/calculate-ratio.ts[2K[1Gsrc/lib/copy-to-clipboard.ts[2K[1Gsrc/lib/dates/constants.ts[2K[1Gsrc/lib/dates/get-hours-difference-from-date.test.ts[2K[1Gsrc/lib/dates/get-hours-difference-from-date.ts[2K[1Gsrc/lib/dom/element-has-scrollbar.ts[2K[1Gsrc/lib/either.ts[2K[1Gsrc/lib/expand-decimals.test.ts[2K[1Gsrc/lib/expand-decimals.ts[2K[1Gsrc/lib/firebase/app-check.ts[2K[1Gsrc/lib/firebase/client-config.ts[2K[1Gsrc/lib/firebase/get-firebase-admin.ts[2K[1Gsrc/lib/firebase/get-firebase-app.ts[2K[1Gsrc/lib/firebase/get-firebase-error-message.ts[2K[1Gsrc/lib/firebase/get-tokens.ts[2K[1Gsrc/lib/firebase/remote-config.ts[2K[1Gsrc/lib/firebase/server-config.ts[2K[1Gsrc/lib/formatters/format-address.ts[2K[1Gsrc/lib/formatters/format-compact-number.test.ts[2K[1Gsrc/lib/formatters/format-compact-number.ts[2K[1Gsrc/lib/formatters/format-compact-usd.test.ts[2K[1Gsrc/lib/formatters/format-compact-usd.ts[2K[1Gsrc/lib/formatters/format-currency.test.ts[2K[1Gsrc/lib/formatters/format-currency.ts[2K[1Gsrc/lib/formatters/format-date.test.ts[2K[1Gsrc/lib/formatters/format-date.ts[2K[1Gsrc/lib/formatters/format-number.test.ts[2K[1Gsrc/lib/formatters/format-number.ts[2K[1Gsrc/lib/formatters/format-percentage-proportion.test.ts[2K[1Gsrc/lib/formatters/format-percentage-proportion.ts[2K[1Gsrc/lib/formatters/format-percentage.test.ts[2K[1Gsrc/lib/formatters/format-percentage.ts[2K[1Gsrc/lib/formatters/format-ratio-value.test.ts[2K[1Gsrc/lib/formatters/format-ratio-value.ts[2K[1Gsrc/lib/formatters/format-time-remaining.test.ts[2K[1Gsrc/lib/formatters/format-time-remaining.ts[2K[1Gsrc/lib/formatters/format-usd.test.ts[2K[1Gsrc/lib/formatters/format-usd.ts[2K[1Gsrc/lib/get-error-message.ts[2K[1Gsrc/lib/global-store.ts[2K[1Gsrc/lib/is-client.ts[2K[1Gsrc/lib/is-cookie-value-true.ts[2K[1Gsrc/lib/is-error-code.ts[2K[1Gsrc/lib/is-local-dev.ts[2K[1Gsrc/lib/is-positive.ts[2K[1Gsrc/lib/logger.ts[2K[1Gsrc/lib/mails/constants.ts[2K[1Gsrc/lib/mails/get-ecomail-config.ts[2K[1Gsrc/lib/mails/send-mail.ts[2K[1Gsrc/lib/mails/types.ts[2K[1Gsrc/lib/polyfills.ts[2K[1Gsrc/lib/query-client.ts[2K[1Gsrc/lib/query-pattern.ts[2K[1Gsrc/lib/sort/sort-alphabetically.ts[2K[1Gsrc/lib/sort/sort-numerically.ts[2K[1Gsrc/lib/sort/sort-wallets.test.ts[2K[1Gsrc/lib/sort/sort-wallets.ts[2K[1Gsrc/lib/strings-equals.test.ts[2K[1Gsrc/lib/strings-equals.ts[2K[1Gsrc/lib/type-checks.ts[2K[1Gsrc/lib/use-media-query-match.ts[2K[1Gsrc/lib/utils.ts[2K[1Gsrc/lib/validators/evm-address-validator.ts[2K[1Gsrc/lib/validators/evm-private-key-validators.ts[2K[1Gsrc/lib/validators/solana-address-validator.ts[2K[1Gsrc/lib/validators/solana-private-key-validator/is-valid-solana-private-key.test.ts[2K[1Gsrc/lib/validators/solana-private-key-validator/is-valid-solana-private-key.ts[2K[1Gsrc/lib/validators/solana-private-key-validator/solana-private-key-validator.ts[2K[1Gsrc/lib/wallet/make-wallet-label.test.ts[2K[1Gsrc/lib/wallet/make-wallet-label.ts[2K[1Gsrc/lib/wallet/validate-input-amount.test.ts[2K[1Gsrc/lib/wallet/validate-input-amount.ts[2K[1Gsrc/middleware.ts[2K[1Gsrc/module/assets/constants.ts[2K[1Gsrc/module/assets/types.ts[2K[1Gsrc/module/assets/use-my-assets.test.ts[2K[1Gsrc/module/assets/use-my-assets.ts[2K[1Gsrc/module/assets/use-native-position.ts[2K[1Gsrc/module/assets/utils.ts[2K[1Gsrc/module/auth/get-firebase-mode-redirect.ts[2K[1Gsrc/module/auth/mfa/components/use-totp-form.ts[2K[1Gsrc/module/auth/mfa/deactivate/use-reauthenticate-disable-mfa-form.ts[2K[1Gsrc/module/auth/mfa/enroll/enroll-mfa.ts[2K[1Gsrc/module/auth/mfa/enroll/revalidate-profile.action.ts[2K[1Gsrc/module/auth/mfa/enroll/send-mfa-notification.ts[2K[1Gsrc/module/auth/mfa/enroll/use-reauthenticate-form.ts[2K[1Gsrc/module/auth/mfa/is-mfa-enabled.ts[2K[1Gsrc/module/auth/mfa/schemas/totp-schema.test.ts[2K[1Gsrc/module/auth/mfa/schemas/totp-schema.ts[2K[1Gsrc/module/auth/reset-password/use-new-password-form.ts[2K[1Gsrc/module/auth/reset-password/use-reset-password-form.ts[2K[1Gsrc/module/auth/revoke-tokens.ts[2K[1Gsrc/module/auth/sign-in/login-action.ts[2K[1Gsrc/module/auth/sign-in/use-signin-form.ts[2K[1Gsrc/module/auth/sign-in/use-signup-form.ts[2K[1Gsrc/module/bot-trading/active-bots/utils.ts[2K[1Gsrc/module/bot-trading/bot-compare/constants.ts[2K[1Gsrc/module/bot-trading/bot-compare/hooks/use-selected-bot-compare-settings.ts[2K[1Gsrc/module/bot-trading/bot-compare/hooks/use-selected-bot-compare.ts[2K[1Gsrc/module/bot-trading/bot-compare/portfolio/use-toggle-activate-bot.ts[2K[1Gsrc/module/bot-trading/bot-compare/types.ts[2K[1Gsrc/module/bot-trading/bot-detail/hooks/use-bot-activity-data.ts[2K[1Gsrc/module/bot-trading/bot-detail/hooks/use-bot-detail-data.ts[2K[1Gsrc/module/bot-trading/bot-detail/hooks/use-bot-detail-settings-data.ts[2K[1Gsrc/module/bot-trading/bot-detail/providers/bot-event-emitter.ts[2K[1Gsrc/module/bot-trading/bot-detail/providers/bot-transaction-update-provider.ts[2K[1Gsrc/module/bot-trading/bot-detail/utils/bot-trade-schema.ts[2K[1Gsrc/module/bot-trading/bot-detail/utils/is-opened-bot-trade.ts[2K[1Gsrc/module/bot-trading/bot-detail/utils/revalidate-bot-trading-page.action.ts[2K[1Gsrc/module/bot-trading/bot-portfolio/use-bot-portfolio-infinite.ts[2K[1Gsrc/module/bot-trading/bot-trade-detail/use-bot-trade-detail-data.ts[2K[1Gsrc/module/bot-trading/bot-trade-detail/utils/use-sell-reason.ts[2K[1Gsrc/module/bot-trading/bot-trades/utils/sort-bot-trades.ts[2K[1Gsrc/module/bot-trading/bot-withdraw/hooks/use-select-wallet-form.ts[2K[1Gsrc/module/bot-trading/bot-withdraw/hooks/use-withdraw-amount.ts[2K[1Gsrc/module/bot-trading/bot-withdraw/hooks/use-withdraw-form.ts[2K[1Gsrc/module/bot-trading/bot-withdraw/types.ts[2K[1Gsrc/module/bot-trading/bot-withdraw/utils/is-error-code.ts[2K[1Gsrc/module/bot-trading/bot-wizard/components/advanced-bot-trading-settings/settings-config.ts[2K[1Gsrc/module/bot-trading/bot-wizard/components/launching-bot-dialog/steps/utils.ts[2K[1Gsrc/module/bot-trading/bot-wizard/constants.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-bot-name-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-bot-wizard-api.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-bot-wizard-form-data.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-bot-wizard-step.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-bot-wizard-summary.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-choose-volume-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-choose-your-bot-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-daily-limit-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-define-liquidity-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-exit-wizard.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-holder-range-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-market-cap-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-scroll-to-content-in-mount.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-10-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-12-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-13-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-14-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-15-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-17-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-18-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-3-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-4-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-7-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-8-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-step-9-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-trade-amount-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-trade-profit-target-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-trade-stop-loss-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-transactions-ratio-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/hooks/use-volume-proportion-form.ts[2K[1Gsrc/module/bot-trading/bot-wizard/utils/is-bot-draft-ready.ts[2K[1Gsrc/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url.test.ts[2K[1Gsrc/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url.ts[2K[1Gsrc/module/bot-trading/bot-wizard/utils/selected-value-validation.ts[2K[1Gsrc/module/bot-trading/bot-wizard/utils/slider-values-utils.ts[2K[1Gsrc/module/bot-trading/bots-config.ts[2K[1Gsrc/module/bot-trading/coming-soon/type.ts[2K[1Gsrc/module/bot-trading/coming-soon/use-coming-soon.ts[2K[1Gsrc/module/bot-trading/config.ts[2K[1Gsrc/module/bot-trading/hooks/use-reset-daily-limit.ts[2K[1Gsrc/module/bot-trading/hooks/use-time-range-label.ts[2K[1Gsrc/module/bot-trading/use-bot-trading-data.ts[2K[1Gsrc/module/bot-trading/use-public-bot-trading-data.ts[2K[1Gsrc/module/bot-trading/utils/check-is-create-bot-enabled.ts[2K[1Gsrc/module/bot-trading/utils/formatters.ts[2K[1Gsrc/module/bot-trading/utils/get-bot-wallet.ts[2K[1Gsrc/module/bot-trading/utils/is-native-currency-transfer.ts[2K[1Gsrc/module/bot-trading/utils/sort-active-bots-first.ts[2K[1Gsrc/module/bot-trading/utils/sort-parameter-utils.ts[2K[1Gsrc/module/bot-trading/utils/timerange-utils.ts[2K[1Gsrc/module/home/<USER>/get-allocation-overview.test.ts[2K[1Gsrc/module/home/<USER>/get-allocation-overview.ts[2K[1Gsrc/module/home/<USER>/index.ts[2K[1Gsrc/module/home/<USER>/use-asset-allocation-data.ts[2K[1Gsrc/module/i18n/constants.ts[2K[1G[[33mwarn[39m] src/module/i18n/constants.ts
src/module/i18n/lib/get-cookie-locale.ts[2K[1Gsrc/module/i18n/lib/get-selected-locale.ts[2K[1Gsrc/module/i18n/lib/select-locale.ts[2K[1Gsrc/module/i18n/next-intl.d.ts[2K[1Gsrc/module/i18n/request.ts[2K[1Gsrc/module/i18n/utils/make-icon-source.ts[2K[1Gsrc/module/i18n/utils/make-language.ts[2K[1Gsrc/module/i18n/utils/make-locale.ts[2K[1Gsrc/module/invite/components/use-create-link-form.ts[2K[1Gsrc/module/league/rewards/reward-config.ts[2K[1Gsrc/module/league/streak/components/streak-configs.ts[2K[1Gsrc/module/league/streak/components/streak-rewards/utils.ts[2K[1Gsrc/module/league/streak/streak-store.ts[2K[1Gsrc/module/league/streak/types.ts[2K[1Gsrc/module/league/streak/use-streak-config.ts[2K[1Gsrc/module/league/streak/utils.ts[2K[1Gsrc/module/league/utils/format-reward-number.ts[2K[1Gsrc/module/maintenance/get-maintenance-config.ts[2K[1Gsrc/module/maintenance/maintenance-checker.ts[2K[1Gsrc/module/manual-trading/last-transactions/last-transactions-request-params.ts[2K[1Gsrc/module/manual-trading/last-transactions/use-last-transactions-infinite.ts[2K[1Gsrc/module/manual-trading/use-hot-tokens.ts[2K[1Gsrc/module/multichain/get-chain-config.ts[2K[1Gsrc/module/onboarding/onboarding-step-schema.ts[2K[1Gsrc/module/profile/components/get-account-referral-data.ts[2K[1Gsrc/module/referral/components/use-tab-query.ts[2K[1Gsrc/module/routing/get-valid-redirect-url.test.ts[2K[1Gsrc/module/routing/get-valid-redirect-url.ts[2K[1Gsrc/module/routing/use-redirect-url.ts[2K[1Gsrc/module/setup/import-wallet.action.ts[2K[1Gsrc/module/setup/use-create-wallet-form.ts[2K[1Gsrc/module/setup/use-import-wallet-form.ts[2K[1Gsrc/module/setup/wallet-deposit-dialog/set-dialog-shown-cookie.action.ts[2K[1Gsrc/module/token-detail/actions/buy-token.ts[2K[1Gsrc/module/token-detail/actions/sell-token.ts[2K[1Gsrc/module/token-detail/buy-warning/utils.ts[2K[1Gsrc/module/token-detail/get-advanced-chart-config.ts[2K[1Gsrc/module/token-detail/hooks/use-basic-token-chart-data.ts[2K[1Gsrc/module/token-detail/manual-trading/result-dialog/get-trade-result.ts[2K[1Gsrc/module/token-detail/manual-trading/result-dialog/get-transaction-status.ts[2K[1Gsrc/module/token-detail/manual-trading/result-dialog/use-progress-labels.ts[2K[1Gsrc/module/token-detail/manual-trading/use-manual-trading-form.ts[2K[1Gsrc/module/token-detail/manual-trading/use-token-balance.ts[2K[1Gsrc/module/token-detail/manual-trading/utils/check-has-enough-balance.ts[2K[1Gsrc/module/token-detail/manual-trading/utils/get-trade-success-message.ts[2K[1Gsrc/module/token-detail/manual-trading/utils/use-trade-status-fetch-count.ts[2K[1Gsrc/module/token-detail/token-audit/scroll-to-audit.ts[2K[1Gsrc/module/token-detail/token-chart-provider.ts[2K[1Gsrc/module/token-detail/utils/get-available-timeframes.ts[2K[1Gsrc/module/wallet-detail/get-user-wallet.action.ts[2K[1Gsrc/module/wallet-detail/hooks/use-edit-wallet-form.ts[2K[1Gsrc/module/wallet-detail/hooks/use-filtered-wallets.ts[2K[1Gsrc/module/wallet-detail/hooks/use-password-form.ts[2K[1Gsrc/module/wallet-detail/hooks/use-wallets.ts[2K[1Gsrc/module/wallet-detail/update-wallet-custom-name.action.ts[2K[1Gsrc/module/wallet-detail/wallet-deposit-info/constants.ts[2K[1Gsrc/module/wallet-detail/wallet-deposit-info/download-qr-code.ts[2K[1Gsrc/module/wallet-withdraw/hooks/use-password-error-message.ts[2K[1Gsrc/module/wallet-withdraw/hooks/use-withdraw-form.ts[2K[1Gsrc/module/wallet-withdraw/use-withdraw.ts[2K[1Gsrc/module/wallet-withdraw/utils/is-error-code.ts[2K[1Gsrc/module/wallets/constants.ts[2K[1Gsrc/module/wallets/use-tabs-literal.ts[2K[1Gsrc/utils/dom.ts[2K[1Gsrc/utils/scroll-to-element.ts[2K[1Gsrc/utils/search-params.test.ts[2K[1Gsrc/utils/search-params.ts[2K[1Gsrc/utils/session-storage.ts[2K[1Gsrc/utils/stop-propagation.ts[2K[1Gtests/auth.setup.ts[2K[1Gtests/helpers/basic-auth.ts[2K[1Gtests/helpers/skip-intro.ts[2K[1Gtests/mocks/bot-mocks.ts[2K[1Gtests/pages/bot-trading-page.ts[2K[1Gtests/pages/home-page.ts[2K[1Gtests/pages/manual-trading-page.ts[2K[1Gtests/pages/onboarding-page.ts[2K[1Gtests/specs/bot-trading/bot-detail-inactive-bot-state-text.spec.ts[2K[1Gtests/specs/bot-trading/bot-detail-insufficient-balance.spec.ts[2K[1Gtests/specs/bot-trading/bot-trading-portfolio.spec.ts[2K[1Gtests/specs/bot-trading/bot-trading-uniform-timeranges.spec.ts[2K[1Gtests/specs/buy.spec.ts[2K[1Gtests/specs/chain-selector.spec.ts[2K[1Gtests/specs/log-in.spec.ts[2K[1Gtests/specs/onboarding.spec.ts[2K[1Gtests/specs/public.spec.ts[2K[1Gtests/specs/unauth-states/bot-trading-unauth.spec.ts[2K[1Gtests/specs/unauth-states/home-page-unauth.spec.ts[2K[1Gtests/specs/unauth-states/manual-trading-unauth.spec.ts[2K[1Gtests/tsconfig.json[2K[1Gtests/utils/mock-api.ts[2K[1Gtsconfig.json[2K[1Gvitest.config.ts[2K[1Gvitest.global.ts[2K[1Gvitest.setup.ts[2K[1G[[33mwarn[39m] Code style issues fixed in the above file.
