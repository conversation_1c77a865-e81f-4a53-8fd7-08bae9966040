

> fatbot@1.2.3 tc /Users/<USER>/Workspace/fatbot/apps/fatbot
> tsc --noEmit --skipLibCheck

[96msrc/api/mocks/user-streak.ts[0m:[93m3[0m:[93m14[0m - [91merror[0m[90m TS2741: [0mProperty 'streakState' is missing in type '{ userId: string; daysInStreak: number; currentMultiplier: string; daysToNextStreak: number; nextMultiplier: string; streakExpiresAt: string; isThresholdDay: true; thresholds: ({ daysInStreak: number; multiplier: string; completed: true; } | { ...; })[]; streakDates: ({ ...; } | { ...; })[]; }' but required in type 'GetUserStreakResult'.

[7m3[0m export const userStreakMock: GetUserStreakResult = {
[7m [0m [91m             ~~~~~~~~~~~~~~[0m

  [96msrc/lib/api.ts[0m:[93m1163[0m:[93m3[0m
    [7m1163[0m   streakState: StreakState;
    [7m    [0m [96m  ~~~~~~~~~~~[0m
    'streakState' is declared here.

[96msrc/components/__tests__/animate-number.test.tsx[0m:[93m36[0m:[93m50[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m36[0m     expect(screen.getByTestId('animated-value')).toHaveTextContent('100');
[7m  [0m [91m                                                 ~~~~~~~~~~~~~~~~~[0m

[96msrc/components/__tests__/animate-number.test.tsx[0m:[93m89[0m:[93m50[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m89[0m     expect(screen.getByTestId('animated-value')).toHaveTextContent('150');
[7m  [0m [91m                                                 ~~~~~~~~~~~~~~~~~[0m

[96msrc/components/animate-number.test.tsx[0m:[93m36[0m:[93m50[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m36[0m     expect(screen.getByTestId('animated-value')).toHaveTextContent('100');
[7m  [0m [91m                                                 ~~~~~~~~~~~~~~~~~[0m

[96msrc/components/animate-number.test.tsx[0m:[93m89[0m:[93m50[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m89[0m     expect(screen.getByTestId('animated-value')).toHaveTextContent('150');
[7m  [0m [91m                                                 ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[0m:[93m19[0m:[93m58[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m19[0m     expect(screen.getByTestId('buy-and-sell-buy-count')).toHaveTextContent(
[7m  [0m [91m                                                         ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[0m:[93m22[0m:[93m59[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m22[0m     expect(screen.getByTestId('buy-and-sell-sell-count')).toHaveTextContent(
[7m  [0m [91m                                                          ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[0m:[93m37[0m:[93m58[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m37[0m     expect(screen.getByTestId('buy-and-sell-buy-count')).toHaveTextContent(
[7m  [0m [91m                                                         ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[0m:[93m40[0m:[93m59[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m40[0m     expect(screen.getByTestId('buy-and-sell-sell-count')).toHaveTextContent(
[7m  [0m [91m                                                          ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m148[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeEmptyDOMElement' does not exist on type 'Assertion<HTMLElement>'.

[7m148[0m     expect(container).toBeEmptyDOMElement();
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m158[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeEmptyDOMElement' does not exist on type 'Assertion<HTMLElement>'.

[7m158[0m     expect(container).toBeEmptyDOMElement();
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m176[0m:[93m48[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement>'.

[7m176[0m     expect(getByTestId('streak-loose-dialog')).toBeInTheDocument();
[7m   [0m [91m                                               ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m197[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeEmptyDOMElement' does not exist on type 'Assertion<HTMLElement>'.

[7m197[0m     expect(container).toBeEmptyDOMElement();
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m218[0m:[93m47[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement>'.

[7m218[0m     expect(getByTestId('lost-streak-dialog')).toBeInTheDocument();
[7m   [0m [91m                                              ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m241[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeEmptyDOMElement' does not exist on type 'Assertion<HTMLElement>'.

[7m241[0m     expect(container).toBeEmptyDOMElement();
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m260[0m:[93m53[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement>'.

[7m260[0m     expect(getByTestId('streak-multiplier-dialog')).toBeInTheDocument();
[7m   [0m [91m                                                    ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m282[0m:[93m46[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement>'.

[7m282[0m     expect(getByTestId('day-streak-dialog')).toBeInTheDocument();
[7m   [0m [91m                                             ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m303[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeEmptyDOMElement' does not exist on type 'Assertion<HTMLElement>'.

[7m303[0m     expect(container).toBeEmptyDOMElement();
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m320[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeEmptyDOMElement' does not exist on type 'Assertion<HTMLElement>'.

[7m320[0m     expect(container).toBeEmptyDOMElement();
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m342[0m:[93m48[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement>'.

[7m342[0m     expect(getByTestId('streak-loose-dialog')).toBeInTheDocument();
[7m   [0m [91m                                               ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m343[0m:[93m52[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement | null>'.

[7m343[0m     expect(queryByTestId('day-streak-dialog')).not.toBeInTheDocument();
[7m   [0m [91m                                                   ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m344[0m:[93m59[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement | null>'.

[7m344[0m     expect(queryByTestId('streak-multiplier-dialog')).not.toBeInTheDocument();
[7m   [0m [91m                                                          ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m366[0m:[93m47[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement>'.

[7m366[0m     expect(getByTestId('lost-streak-dialog')).toBeInTheDocument();
[7m   [0m [91m                                              ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m367[0m:[93m52[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement | null>'.

[7m367[0m     expect(queryByTestId('day-streak-dialog')).not.toBeInTheDocument();
[7m   [0m [91m                                                   ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m386[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeEmptyDOMElement' does not exist on type 'Assertion<HTMLElement>'.

[7m386[0m     expect(container).toBeEmptyDOMElement();
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/module/league/streak/streak-manager.test.tsx[0m:[93m407[0m:[93m23[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeEmptyDOMElement' does not exist on type 'Assertion<HTMLElement>'.

[7m407[0m     expect(container).toBeEmptyDOMElement();
[7m   [0m [91m                      ~~~~~~~~~~~~~~~~~~~[0m

[96msrc/module/referral/components/claim-referral-dialog-link.test.tsx[0m:[93m16[0m:[93m62[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveAttribute' does not exist on type 'Assertion<HTMLElement>'.

[7m16[0m     expect(screen.getByTestId('claim-referral-dialog-link')).toHaveAttribute(
[7m  [0m [91m                                                             ~~~~~~~~~~~~~~~[0m

[96msrc/module/referral/components/claim-referral-dialog-link.test.tsx[0m:[93m21[0m:[93m62[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveAttribute' does not exist on type 'Assertion<HTMLElement>'.

[7m21[0m     expect(screen.getByTestId('claim-referral-dialog-link')).toHaveAttribute(
[7m  [0m [91m                                                             ~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/native-presets.test.tsx[0m:[93m26[0m:[93m20[0m - [91merror[0m[90m TS2339: [0mProperty 'toBeInTheDocument' does not exist on type 'Assertion<HTMLElement>'.

[7m26[0m       expect(icon).toBeInTheDocument();
[7m  [0m [91m                   ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/token-stats.test.tsx[0m:[93m15[0m:[93m58[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m15[0m     expect(screen.getByTestId('token-stats-market-cap')).toHaveTextContent('N/A');
[7m  [0m [91m                                                         ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/token-stats.test.tsx[0m:[93m16[0m:[93m51[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m16[0m     expect(screen.getByTestId('token-stats-fdv')).toHaveTextContent('N/A');
[7m  [0m [91m                                                  ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/token-stats.test.tsx[0m:[93m17[0m:[93m54[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m17[0m     expect(screen.getByTestId('token-stats-volume')).toHaveTextContent('1.1K');
[7m  [0m [91m                                                     ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/token-stats.test.tsx[0m:[93m18[0m:[93m57[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m18[0m     expect(screen.getByTestId('token-stats-liquidity')).toHaveTextContent('N/A');
[7m  [0m [91m                                                        ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/token-stats.test.tsx[0m:[93m28[0m:[93m58[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m28[0m     expect(screen.getByTestId('token-stats-market-cap')).toHaveTextContent('1M');
[7m  [0m [91m                                                         ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/token-stats.test.tsx[0m:[93m29[0m:[93m51[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m29[0m     expect(screen.getByTestId('token-stats-fdv')).toHaveTextContent('100');
[7m  [0m [91m                                                  ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/token-stats.test.tsx[0m:[93m30[0m:[93m54[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m30[0m     expect(screen.getByTestId('token-stats-volume')).toHaveTextContent('1.1K');
[7m  [0m [91m                                                     ~~~~~~~~~~~~~~~~~[0m

[96msrc/module/token-detail/__tests__/token-stats.test.tsx[0m:[93m31[0m:[93m57[0m - [91merror[0m[90m TS2339: [0mProperty 'toHaveTextContent' does not exist on type 'Assertion<HTMLElement>'.

[7m31[0m     expect(screen.getByTestId('token-stats-liquidity')).toHaveTextContent('100K');
[7m  [0m [91m                                                        ~~~~~~~~~~~~~~~~~[0m


Found 37 errors in 8 files.

Errors  Files
     1  src/api/mocks/user-streak.ts[90m:3[0m
     2  src/components/__tests__/animate-number.test.tsx[90m:36[0m
     2  src/components/animate-number.test.tsx[90m:36[0m
     4  src/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[90m:19[0m
    17  src/module/league/streak/streak-manager.test.tsx[90m:148[0m
     2  src/module/referral/components/claim-referral-dialog-link.test.tsx[90m:16[0m
     1  src/module/token-detail/__tests__/native-presets.test.tsx[90m:26[0m
     8  src/module/token-detail/__tests__/token-stats.test.tsx[90m:15[0m
[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 2.[39m
