

> fatbot@1.2.3 test /Users/<USER>/Workspace/fatbot/apps/fatbot
> vitest

[?25l
[1m[44m DEV [49m[22m [34mv3.2.3 [39m[90m/Users/<USER>/Workspace/fatbot/apps/fatbot[39m

[?2026h
[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m [queued][22m

[2m Test Files [22m[1m[32m0 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m0 passed[39m[22m[90m (0)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m504ms
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/league/streak/streak-manager.test.tsx[2m 0/14[22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/native-presets.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/token-stats.test.tsx[2m [queued][22m

[2m Test Files [22m[1m[32m0 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m0 passed[39m[22m[90m (14)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m807ms
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/module/league/streak/streak-manager.test.tsx [2m([22m[2m14 tests[22m[2m)[22m[32m 35[2mms[22m[39m
 [32m✓[39m src/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[32m 36[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[2m 2/2[22m
[1m[33m ❯ [39m[22msrc/module/league/streak/streak-manager.test.tsx[2m 14/14[22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/native-presets.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/token-stats.test.tsx[2m 1/2[22m

[2m Test Files [22m[1m[32m2 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m17 passed[39m[22m[90m (18)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m907ms
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/module/token-detail/__tests__/token-stats.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[32m 36[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[2m 2/2[22m
[1m[33m ❯ [39m[22msrc/module/league/streak/streak-manager.test.tsx[2m 14/14[22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/native-presets.test.tsx[2m 1/5[22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/token-stats.test.tsx[2m 2/2[22m

[2m Test Files [22m[1m[32m3 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m19 passed[39m[22m[90m (23)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m1.11s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/module/token-detail/__tests__/native-presets.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[32m 96[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/animate-number.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[2m 2/2[22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/native-presets.test.tsx[2m 5/5[22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/token-stats.test.tsx[2m 2/2[22m

[2m Test Files [22m[1m[32m4 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m23 passed[39m[22m[90m (23)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m1.31s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/components/__tests__/animate-number.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/components/animate-number.test.tsx[2m 0/4[22m
[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/assets/use-my-assets.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/token-detail/__tests__/native-presets.test.tsx[2m 5/5[22m

[2m Test Files [22m[1m[32m4 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m23 passed[39m[22m[90m (27)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m1.41s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/components/animate-number.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[32m 24[2mms[22m[39m
 [32m✓[39m src/components/__tests__/animate-number.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[32m 23[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/__tests__/animate-number.test.tsx[2m 4/4[22m
[1m[33m ❯ [39m[22msrc/components/animate-number.test.tsx[2m 4/4[22m
[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m 1/8[22m
[1m[33m ❯ [39m[22msrc/module/assets/use-my-assets.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/referral/components/claim-referral-dialog-link.test.tsx[2m [queued][22m

[2m Test Files [22m[1m[32m6 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m32 passed[39m[22m[90m (39)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m1.62s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/hooks/use-relative-date-format.test.tsx [2m([22m[2m8 tests[22m[2m)[22m[32m 38[2mms[22m[39m
 [32m✓[39m src/module/assets/use-my-assets.test.ts [2m([22m[2m7 tests[22m[2m)[22m[32m 19[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/__tests__/animate-number.test.tsx[2m 4/4[22m
[1m[33m ❯ [39m[22msrc/components/animate-number.test.tsx[2m 4/4[22m
[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m 8/8[22m
[1m[33m ❯ [39m[22msrc/module/assets/use-my-assets.test.ts[2m 7/7[22m
[1m[33m ❯ [39m[22msrc/module/referral/components/claim-referral-dialog-link.test.tsx[2m 0/1[22m

[2m Test Files [22m[1m[32m8 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m46 passed[39m[22m[90m (47)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m1.72s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/module/referral/components/claim-referral-dialog-link.test.tsx [2m([22m[2m1 test[22m[2m)[22m[32m 23[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/__tests__/animate-number.test.tsx[2m 4/4[22m
[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m 8/8[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-ratio-value.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/assets/use-my-assets.test.ts[2m 7/7[22m
[1m[33m ❯ [39m[22msrc/module/referral/components/claim-referral-dialog-link.test.tsx[2m 1/1[22m

[2m Test Files [22m[1m[32m9 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m47 passed[39m[22m[90m (47)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m1.93s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/formatters/format-ratio-value.test.ts [2m([22m[2m9 tests[22m[2m)[22m[32m 14[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/hooks/use-relative-date-format.test.tsx[2m 8/8[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-ratio-value.test.ts[2m 9/9[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-usd.test.ts[2m 0/10[22m
[1m[33m ❯ [39m[22msrc/module/assets/use-my-assets.test.ts[2m 7/7[22m
[1m[33m ❯ [39m[22msrc/module/referral/components/claim-referral-dialog-link.test.tsx[2m 1/1[22m

[2m Test Files [22m[1m[32m10 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m56 passed[39m[22m[90m (66)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m2.03s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/formatters/format-usd.test.ts [2m([22m[2m10 tests[22m[2m)[22m[32m 18[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-number.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-number.test.ts[2m 0/10[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-ratio-value.test.ts[2m 9/9[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-usd.test.ts[2m 10/10[22m
[1m[33m ❯ [39m[22msrc/module/referral/components/claim-referral-dialog-link.test.tsx[2m 1/1[22m

[2m Test Files [22m[1m[32m11 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m66 passed[39m[22m[90m (76)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m2.13s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/formatters/format-number.test.ts [2m([22m[2m10 tests[22m[2m)[22m[32m 19[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-number.test.ts[2m 1/16[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-usd.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-number.test.ts[2m 10/10[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-ratio-value.test.ts[2m 9/9[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-usd.test.ts[2m 10/10[22m

[2m Test Files [22m[1m[32m12 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m77 passed[39m[22m[90m (92)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m2.23s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/formatters/format-compact-number.test.ts [2m([22m[2m16 tests[22m[2m)[22m[32m 16[2mms[22m[39m
 [32m✓[39m src/lib/formatters/format-compact-usd.test.ts [2m([22m[2m2 tests[22m[2m)[22m[32m 13[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-number.test.ts[2m 16/16[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-usd.test.ts[2m 2/2[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-number.test.ts[2m 10/10[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-percentage.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-usd.test.ts[2m 10/10[22m

[2m Test Files [22m[1m[32m14 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m94 passed[39m[22m[90m (94)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m2.43s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/hooks/use-sorted-wallets.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-number.test.ts[2m 16/16[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-usd.test.ts[2m 2/2[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-number.test.ts[2m 10/10[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-percentage.test.ts[2m 1/10[22m

[2m Test Files [22m[1m[32m14 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m95 passed[39m[22m[90m (104)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m2.53s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/formatters/format-percentage.test.ts [2m([22m[2m10 tests[22m[2m)[22m[32m 16[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/charts/area-chart/utils.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/hooks/use-sorted-wallets.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-number.test.ts[2m 16/16[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-compact-usd.test.ts[2m 2/2[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-percentage.test.ts[2m 10/10[22m

[2m Test Files [22m[1m[32m15 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m104 passed[39m[22m[90m (104)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m2.63s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/components/charts/area-chart/utils.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/env/validator.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/hooks/use-sorted-wallets.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/dates/get-hours-difference-from-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-percentage.test.ts[2m 10/10[22m

[2m Test Files [22m[1m[32m15 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m104 passed[39m[22m[90m (104)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m2.73s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/env/validator.test.ts [2m([22m[2m13 tests[22m[2m)[22m[32m 6[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/charts/area-chart/utils.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/env/validator.test.ts[2m 13/13[22m
[1m[33m ❯ [39m[22msrc/hooks/use-sorted-wallets.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/dates/get-hours-difference-from-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-percentage.test.ts[2m 10/10[22m

[2m Test Files [22m[1m[32m16 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m117 passed[39m[22m[90m (117)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m2.93s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/components/charts/area-chart/utils.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/env/validator.test.ts[2m 13/13[22m
[1m[33m ❯ [39m[22msrc/hooks/use-sorted-wallets.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/dates/get-hours-difference-from-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url.test.ts[2m 1/7[22m

[2m Test Files [22m[1m[32m16 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m118 passed[39m[22m[90m (124)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m3.03s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url.test.ts [2m([22m[2m7 tests[22m[2m)[22m[32m 5[2mms[22m[39m
 [32m✓[39m src/hooks/use-sorted-wallets.test.ts [2m([22m[2m3 tests[22m[2m)[22m[32m 11[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/charts/area-chart/utils.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/hooks/use-sorted-wallets.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/lib/dates/get-hours-difference-from-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-time-remaining.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url.test.ts[2m 7/7[22m

[2m Test Files [22m[1m[32m18 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m127 passed[39m[22m[90m (127)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m3.24s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/components/charts/area-chart/utils.test.ts[2m 0/13[22m
[1m[33m ❯ [39m[22msrc/hooks/use-sorted-wallets.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/lib/dates/get-hours-difference-from-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-time-remaining.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url.test.ts[2m 7/7[22m

[2m Test Files [22m[1m[32m18 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m127 passed[39m[22m[90m (140)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m3.44s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/components/charts/area-chart/utils.test.ts [2m([22m[2m13 tests[22m[2m)[22m[32m 5[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/charts/area-chart/utils.test.ts[2m 13/13[22m
[1m[33m ❯ [39m[22msrc/hooks/use-sorted-wallets.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/lib/dates/get-hours-difference-from-date.test.ts[2m 1/10[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-currency.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-time-remaining.test.ts[2m [queued][22m

[2m Test Files [22m[1m[32m19 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m141 passed[39m[22m[90m (150)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m3.54s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/dates/get-hours-difference-from-date.test.ts [2m([22m[2m10 tests[22m[2m)[22m[32m 5[2mms[22m[39m
 [32m✓[39m src/utils/search-params.test.ts [2m([22m[2m8 tests[22m[2m)[22m[32m 4[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/components/charts/area-chart/utils.test.ts[2m 13/13[22m
[1m[33m ❯ [39m[22msrc/lib/dates/get-hours-difference-from-date.test.ts[2m 10/10[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-currency.test.ts[2m 1/3[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-time-remaining.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/utils/search-params.test.ts[2m 8/8[22m

[2m Test Files [22m[1m[32m21 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m159 passed[39m[22m[90m (161)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m3.64s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/formatters/format-currency.test.ts [2m([22m[2m3 tests[22m[2m)[22m[32m 4[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/dates/get-hours-difference-from-date.test.ts[2m 10/10[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-currency.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-time-remaining.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/home/<USER>/get-allocation-overview.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/utils/search-params.test.ts[2m 8/8[22m

[2m Test Files [22m[1m[32m22 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m161 passed[39m[22m[90m (161)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m3.84s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/module/home/<USER>/get-allocation-overview.test.ts [2m([22m[2m6 tests[22m[2m)[22m[32m 3[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/formatters/format-currency.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-time-remaining.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/sort/sort-wallets.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/home/<USER>/get-allocation-overview.test.ts[2m 6/6[22m
[1m[33m ❯ [39m[22msrc/utils/search-params.test.ts[2m 8/8[22m

[2m Test Files [22m[1m[32m23 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m167 passed[39m[22m[90m (167)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m4.04s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/formatters/format-time-remaining.test.ts [2m([22m[2m6 tests[22m[2m)[22m[32m 7[2mms[22m[39m
 [32m✓[39m src/lib/sort/sort-wallets.test.ts [2m([22m[2m3 tests[22m[2m)[22m[32m 2[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/calculate-ratio.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-time-remaining.test.ts[2m 6/6[22m
[1m[33m ❯ [39m[22msrc/lib/sort/sort-wallets.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/module/auth/mfa/schemas/totp-schema.test.ts[2m 0/2[22m
[1m[33m ❯ [39m[22msrc/module/home/<USER>/get-allocation-overview.test.ts[2m 6/6[22m

[2m Test Files [22m[1m[32m25 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m176 passed[39m[22m[90m (178)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m4.14s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/module/auth/mfa/schemas/totp-schema.test.ts [2m([22m[2m2 tests[22m[2m)[22m[32m 2[2mms[22m[39m
 [32m✓[39m src/lib/calculate-ratio.test.ts [2m([22m[2m8 tests[22m[2m)[22m[32m 3[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/calculate-ratio.test.ts[2m 8/8[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-time-remaining.test.ts[2m 6/6[22m
[1m[33m ❯ [39m[22msrc/lib/sort/sort-wallets.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/module/auth/mfa/schemas/totp-schema.test.ts[2m 2/2[22m

[2m Test Files [22m[1m[32m27 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m186 passed[39m[22m[90m (186)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m4.44s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/lib/calculate-ratio.test.ts[2m 8/8[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/wallet/validate-input-amount.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/module/auth/mfa/schemas/totp-schema.test.ts[2m 2/2[22m
[1m[33m ❯ [39m[22msrc/module/routing/get-valid-redirect-url.test.ts[2m [queued][22m

[2m Test Files [22m[1m[32m27 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m186 passed[39m[22m[90m (186)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m4.55s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/module/routing/get-valid-redirect-url.test.ts [2m([22m[2m4 tests[22m[2m)[22m[32m 2[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/calculate-ratio.test.ts[2m 8/8[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/wallet/validate-input-amount.test.ts[2m 0/3[22m
[1m[33m ❯ [39m[22msrc/module/auth/mfa/schemas/totp-schema.test.ts[2m 2/2[22m
[1m[33m ❯ [39m[22msrc/module/routing/get-valid-redirect-url.test.ts[2m 4/4[22m

[2m Test Files [22m[1m[32m28 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m190 passed[39m[22m[90m (193)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m4.65s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/wallet/validate-input-amount.test.ts [2m([22m[2m3 tests[22m[2m)[22m[32m 2[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/addresses-equals.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/strings-equals.test.ts[2m 0/6[22m
[1m[33m ❯ [39m[22msrc/lib/wallet/validate-input-amount.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/module/routing/get-valid-redirect-url.test.ts[2m 4/4[22m

[2m Test Files [22m[1m[32m29 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m193 passed[39m[22m[90m (199)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m4.85s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/strings-equals.test.ts [2m([22m[2m6 tests[22m[2m)[22m[32m 2[2mms[22m[39m
 [32m✓[39m src/lib/addresses-equals.test.ts [2m([22m[2m5 tests[22m[2m)[22m[32m 3[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/formatters/format-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/strings-equals.test.ts[2m 6/6[22m
[1m[33m ❯ [39m[22msrc/lib/wallet/validate-input-amount.test.ts[2m 3/3[22m
[1m[33m ❯ [39m[22msrc/module/routing/get-valid-redirect-url.test.ts[2m 4/4[22m

[2m Test Files [22m[1m[32m31 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m204 passed[39m[22m[90m (204)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m4.95s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K
[1m[33m ❯ [39m[22msrc/lib/formatters/format-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/strings-equals.test.ts[2m 6/6[22m
[1m[33m ❯ [39m[22msrc/lib/validators/solana-private-key-validator/is-valid-solana-private-key.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/wallet/validate-input-amount.test.ts[2m 3/3[22m

[2m Test Files [22m[1m[32m31 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m204 passed[39m[22m[90m (204)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m5.15s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/validators/solana-private-key-validator/is-valid-solana-private-key.test.ts [2m([22m[2m5 tests[22m[2m)[22m[32m 2[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/expand-decimals.test.ts[2m 0/2[22m
[1m[33m ❯ [39m[22msrc/lib/formatters/format-date.test.ts[2m [queued][22m
[1m[33m ❯ [39m[22msrc/lib/strings-equals.test.ts[2m 6/6[22m

[2m Test Files [22m[1m[32m32 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m209 passed[39m[22m[90m (211)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m5.25s
[?2026l[?2026h[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/expand-decimals.test.ts [2m([22m[2m2 tests[22m[2m)[22m[32m 2[2mms[22m[39m
 [32m✓[39m src/lib/formatters/format-date.test.ts [2m([22m[2m4 tests[22m[2m)[22m[32m 3[2mms[22m[39m

[1m[33m ❯ [39m[22msrc/lib/formatters/format-percentage-proportion.test.ts[2m 0/2[22m
[1m[33m ❯ [39m[22msrc/lib/wallet/make-wallet-label.test.ts[2m [queued][22m

[2m Test Files [22m[1m[32m34 passed[39m[22m[90m (36)[39m
[2m      Tests [22m[1m[32m215 passed[39m[22m[90m (217)[39m
[2m   Start at [22m10:16:13
[2m   Duration [22m5.35s
[?2026l[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K[1A[K [32m✓[39m src/lib/formatters/format-percentage-proportion.test.ts [2m([22m[2m2 tests[22m[2m)[22m[32m 2[2mms[22m[39m
 [32m✓[39m src/lib/wallet/make-wallet-label.test.ts [2m([22m[2m3 tests[22m[2m)[22m[32m 2[2mms[22m[39m

[2m Test Files [22m [1m[32m36 passed[39m[22m[90m (36)[39m
[2m      Tests [22m [1m[32m220 passed[39m[22m[90m (220)[39m
[2m   Start at [22m 10:16:13
[2m   Duration [22m 5.47s[2m (transform 634ms, setup 2.06s, collect 7.15s, tests 502ms, environment 10.11s, prepare 2.05s)[22m

[1m[42m PASS [49m[22m [32mWaiting for file changes...[39m
       [2mpress [22m[1mh[22m[2m to show help[22m[2m, [22m[2mpress [22m[1mq[22m[2m to quit[22m
[?25h[?25h
