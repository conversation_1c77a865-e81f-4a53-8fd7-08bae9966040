

> fatbot@1.2.3 lint /Users/<USER>/Workspace/fatbot/apps/fatbot
> eslint --cache . --fix

[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/playwright.config.ts[24m[0m
[0m  [2m88:5[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO: webkit problem with login for some...'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/app/(app)/league/(main)/leaderboard/detail/page.tsx[24m[0m
[0m  [2m18:41[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/app/(app)/league/(main)/leaderboard/page.tsx[24m[0m
[0m  [2m18:35[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/app/(app)/league/(main)/page.tsx[24m[0m
[0m  [2m18:37[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/app/(public)/privacy-policy/page.tsx[24m[0m
[0m   [2m25:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m   [2m32:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m   [2m53:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m   [2m65:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m   [2m77:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m   [2m82:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m   [2m87:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m102:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m113:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m118:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m123:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m132:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/assets/spinner.tsx[24m[0m
[0m  [2m4:8[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/components/__tests__/animate-number.test.tsx[24m[0m
[0m  [2m36:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m89:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/components/animate-number.test.tsx[24m[0m
[0m  [2m36:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m89:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/components/animations/withdraw-success/withdraw-success-loading.tsx[24m[0m
[0m  [2m1128:11[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m1158:11[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/components/chain-selector.tsx[24m[0m
[0m  [2m59:65[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/components/charts/donut-chart/donut-chart-empty.tsx[24m[0m
[0m  [2m32:116[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/components/data-list/my-assets-list/my-assets-row.tsx[24m[0m
[0m  [2m129:50[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/components/wallet-address-card.tsx[24m[0m
[0m  [2m32:74[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/auth/reset-password/use-new-password-form.ts[24m[0m
[0m  [2m53:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/auth/sign-in/sign-in-totp-form.tsx[24m[0m
[0m  [2m19:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/auth/sign-in/use-signin-form.ts[24m[0m
[0m  [2m35:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/active-bots/select-active-bots-content.tsx[24m[0m
[0m  [2m36:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-compare/portfolio/use-toggle-activate-bot.ts[24m[0m
[0m  [2m26:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-detail/bot-detail-daily-limit.tsx[24m[0m
[0m  [2m65:20[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-detail/bot-detail-insufficient-funds.tsx[24m[0m
[0m  [2m52:3[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO: replace with actual wallet data if...'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-trade-detail/bot-trade-detail-header.tsx[24m[0m
[0m  [2m68:15[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-trade-detail/bot-trade-detail-row.tsx[24m[0m
[0m  [2m65:54[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-wizard/components/bot-wizard-back-button.tsx[24m[0m
[0m  [2m11:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-wizard/components/launching-bot-dialog/launching-bot-dialog.tsx[24m[0m
[0m  [2m22:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-wizard/components/launching-bot-dialog/steps/bot-funds-deposit.tsx[24m[0m
[0m  [2m20:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-wizard/components/summary-item-card.tsx[24m[0m
[0m  [2m57:28[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m64:26[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/bot-wizard/steps/summary/draft-bot-summary.tsx[24m[0m
[0m  [2m31:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/components/bot-delete-section.tsx[24m[0m
[0m  [2m24:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/components/bot-trade-row.tsx[24m[0m
[0m   [2m91:41[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m101:52[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/components/bot-trade-timestamps.tsx[24m[0m
[0m  [2m29:65[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m29:68[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m34:62[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m34:65[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/components/bot-trend.tsx[24m[0m
[0m  [2m35:23[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/components/leaderboard/leaderboard-card.tsx[24m[0m
[0m  [2m55:41[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/components/my-bots/buy-and-sell-count.test.tsx[24m[0m
[0m  [2m19:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m22:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m37:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m40:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/bot-trading/utils/get-bot-wallet.ts[24m[0m
[0m  [2m14:5[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO: bots are on SOLANA for now, in the...'  [2mno-warning-comments[22m[0m
[0m  [2m36:5[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO: bots are on SOLANA for now, in the...'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/home/<USER>/asset-allocation-without-bots.tsx[24m[0m
[0m  [2m27:30[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO: update bot tranding data'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/home/<USER>/public-fuel-your-portfolio.tsx[24m[0m
[0m  [2m12:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/home/<USER>
[0m  [2m33:42[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m67:44[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/invite/components/create-invite-link-dialog.tsx[24m[0m
[0m  [2m46:22[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/invite/invite-content.tsx[24m[0m
[0m  [2m24:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/components/carousel.tsx[24m[0m
[0m  [2m214:33[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m233:33[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/components/claiming-card.tsx[24m[0m
[0m  [2m17:45[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m21:65[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/components/earned-fatty-tokens.tsx[24m[0m
[0m  [2m20:117[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m31:43[22m   [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/components/intro-card.tsx[24m[0m
[0m  [2m30:67[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/components/rank-card.tsx[24m[0m
[0m  [2m30:44[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m34:54[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m38:61[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/components/tiny-rank-card.tsx[24m[0m
[0m  [2m78:16[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/league-leaderboard-detail-content.tsx[24m[0m
[0m  [2m52:75[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m58:75[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m64:75[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m70:75[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m76:75[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/rewards/displayed-reward-card.tsx[24m[0m
[0m  [2m41:69[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/rewards/revealred-reward.tsx[24m[0m
[0m  [2m34:35[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m56:73[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/rewards/rewards-carousel.tsx[24m[0m
[0m  [2m182:33[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m212:33[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/streak/components/lost-streak-dialog.tsx[24m[0m
[0m  [2m36:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/streak/components/streak-loose-dialog.tsx[24m[0m
[0m  [2m42:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/streak/components/streak-multiplier-dialog.tsx[24m[0m
[0m  [2m53:27[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/streak/components/streak-rewards/streak-rewards.tsx[24m[0m
[0m  [2m43:36[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/league/streak/streak-manager.test.tsx[24m[0m
[0m  [2m148:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m158:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m176:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m197:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m218:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m241:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m260:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m282:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m303:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m320:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m342:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m343:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m344:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m366:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m367:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m386:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m407:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/manual-trading/components/wallet-select-search.tsx[24m[0m
[0m  [2m64:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/manual-trading/last-transactions-detail/last-transactions-search-content.tsx[24m[0m
[0m  [2m40:8[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO: workaround so we don't have to...'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/manual-trading/my-assets-detail/my-assets-detail-virtual-list.tsx[24m[0m
[0m  [2m40:5[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO: in order to sort all assets...'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/manual-trading/my-assets-detail/my-assets-search-content.tsx[24m[0m
[0m  [2m39:8[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO: workaround so we don't have to...'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/profile/components/select-language-menu-item.tsx[24m[0m
[0m  [2m16:9[22m    [31merror[39m  Unsafe assignment of an error typed value                    [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m20:83[22m   [31merror[39m  Unsafe assignment of an error typed value                    [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m20:96[22m   [31merror[39m  Unsafe member access .iconSource on an `error` typed value   [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m  [2m20:129[22m  [31merror[39m  Unsafe assignment of an error typed value                    [2m@typescript-eslint/no-unsafe-assignment[22m[0m
[0m  [2m20:142[22m  [31merror[39m  Unsafe member access .nativeLabel on an `error` typed value  [2m@typescript-eslint/no-unsafe-member-access[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/referral/components/claim-referral-dialog-link.test.tsx[24m[0m
[0m  [2m16:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m21:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/referral/components/claim-referral-dialog.tsx[24m[0m
[0m  [2m37:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/token-detail/__tests__/native-presets.test.tsx[24m[0m
[0m  [2m26:7[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m[0m

[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/token-detail/__tests__/token-stats.test.tsx[24m[0m
[0m  [2m15:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m16:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m17:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m18:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m28:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m29:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m30:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m  [2m31:5[22m  [31merror[39m  Unsafe call of a(n) `error` type typed value  [2m@typescript-eslint/no-unsafe-call[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/token-detail/manual-trading/advanced-settings.tsx[24m[0m
[0m  [2m167:20[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m  [2m168:45[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/token-detail/manual-trading/gas-fee.tsx[24m[0m
[0m  [2m23:65[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/token-detail/manual-trading/result-dialog/result-dialog.tsx[24m[0m
[0m  [2m70:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/token-detail/my-holdings/my-holdings-list-item.tsx[24m[0m
[0m  [2m79:48[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/token-detail/token-audit/token-audit.tsx[24m[0m
[0m  [2m36:89[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/token-detail/utils/get-available-timeframes.ts[24m[0m
[0m  [2m17:3[22m  [33mwarning[39m  Unexpected 'fixme' comment: 'FIXME: Shouldn't this be >= 67?'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/wallet-detail/delete-wallet-button.tsx[24m[0m
[0m  [2m24:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/wallet-detail/hooks/use-password-form.ts[24m[0m
[0m  [2m25:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/wallet-detail/wallet-deposit-info/wallet-deposit-info.tsx[24m[0m
[0m  [2m65:18[22m  [33mwarning[39m  Unexpected 'todo' comment: 'TODO -- We decided to temporarily...'  [2mno-warning-comments[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/wallet-withdraw/wallet-withdraw-content.tsx[24m[0m
[0m  [2m31:11[22m  [31merror[39m  Avoid referencing unbound methods which may cause unintentional scoping of `this`.[0m
[0mIf your function does not access `this`, you can annotate it with `this: void`, or consider using an arrow function instead  [2m@typescript-eslint/unbound-method[22m[0m
[0m[0m
[0m[4m/Users/<USER>/Workspace/fatbot/apps/fatbot/src/module/wallets/wallet-card.tsx[24m[0m
[0m  [2m143:39[22m  [33mwarning[39m  Cannot have untranslated text in JSX  [2mformatjs/no-literal-string-in-jsx[22m[0m
[0m[0m
[0m[31m[1m✖ 138 problems (61 errors, 77 warnings)[22m[39m[0m
[0m[31m[1m[22m[39m[0m
[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 1.[39m
