

> fatbot-web@1.2.3 format:root:fix /Users/<USER>/Workspace/fatbot
> pnpm run format:root --write


> fatbot-web@1.2.3 format:root /Users/<USER>/Workspace/fatbot
> prettier --ignore-path .prettierignore --check "./*.{js,json,md,yaml}" ".changeset/**/*.md" ".github/**/*.yaml" --write

Checking formatting...
eslint.config.js[2K[1Glint-staged.config.js[2K[1Gpackage.json[2K[1Gpnpm-lock.yaml[2K[1Gpnpm-workspace.yaml[2K[1Gprettier.config.js[2K[1GREADME.md[2K[1Gtsconfig.json[2K[1Gturbo.json[2K[1G.changeset/README.md[2K[1G.github/workflows/pr_to_gitlab.yaml[2K[1GAll matched files use Prettier code style!
