import { expect, type Page } from '@playwright/test';

export class HomePage {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  // Dynamic locators
  get setUpWalletButton() {
    return this.page.getByRole('button', { name: /set up wallet/i });
  }

  get userIconLink() {
    return this.page.getByTestId('user-profile-icon');
  }

  get depositButton() {
    return this.page.getByRole('button', { name: /deposit/i });
  }

  get inviteFriendsLink() {
    return this.page.getByRole('link', { name: /invite friends/i });
  }

  get myTopPerformingLink() {
    return this.page.getByRole('link', { name: 'My Top Performing' });
  }

  // Test ID getters
  get totalPortfolioValue() {
    return this.page.getByTestId('total-portfolio-value').first();
  }

  get assetAllocationValue() {
    return this.page.getByTestId('asset-allocation-value').first();
  }

  get totalProfitValue() {
    return this.page.getByTestId('total-profit-value').first();
  }

  get total1dChangeValue() {
    return this.page.getByTestId('total-1d-change-value').first();
  }

  // Actions
  async clickSetUpWalletButton() {
    await this.setUpWalletButton.scrollIntoViewIfNeeded();
    await this.setUpWalletButton.click();
  }

  async clickMyTopPerformingLink() {
    await this.myTopPerformingLink.scrollIntoViewIfNeeded();
    await this.myTopPerformingLink.click();
  }

  // Redirect check - text based
  async expectRedirectToAuth() {
    await expect(this.page.getByRole('button', { name: /sign up/i })).toBeVisible();
  }
}
