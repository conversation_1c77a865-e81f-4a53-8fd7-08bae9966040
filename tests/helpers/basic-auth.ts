import type { Page } from '@playwright/test';

export async function basicAuth(page: Page) {
  // Set HTTP credentials for basic authentication
  const basicAuthUser = process.env.PLAYWRIGHT_BASIC_AUTH_USER;
  const basicAuthPassword = process.env.PLAYWRIGHT_BASIC_AUTH_PASSWORD;

  if (!basicAuthUser || !basicAuthPassword) {
    throw new Error(
      'PLAYWRIGHT_BASIC_AUTH_USER and PLAYWRIGHT_BASIC_AUTH_PASSWORD must be set in environment variables',
    );
  }

  await page.setExtraHTTPHeaders({
    Authorization: 'Basic ' + Buffer.from(`${basicAuthUser}:${basicAuthPassword}`).toString('base64'),
  });
}
