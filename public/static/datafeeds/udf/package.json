{"private": true, "dependencies": {"@jridgewell/gen-mapping": "0.3.5", "tslib": "2.5.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "~15.3.0", "@rollup/plugin-terser": "~0.4.4", "rollup": "~4.24.4", "typescript": "5.5.4"}, "scripts": {"compile": "tsc", "bundle-js": "rollup -c rollup.config.mjs", "build": "npm run compile && npm run bundle-js"}, "type": "module", "main": "./lib/udf-compatible-datafeed.js", "types": "./types.d.ts"}