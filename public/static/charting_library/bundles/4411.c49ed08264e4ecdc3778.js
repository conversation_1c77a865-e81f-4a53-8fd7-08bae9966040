(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4411],{88317:e=>{e.exports={pills:"pills-PVWoXu5j",primary:"primary-PVWoXu5j",gray:"gray-PVWoXu5j",selected:"selected-PVWoXu5j",grouped:"grouped-PVWoXu5j",active:"active-PVWoXu5j",disableActiveOnTouch:"disableActiveOnTouch-PVWoXu5j",disableActiveStateStyles:"disableActiveStateStyles-PVWoXu5j",withGrouped:"withGrouped-PVWoXu5j","quiet-primary":"quiet-primary-PVWoXu5j",green:"green-PVWoXu5j",red:"red-PVWoXu5j",blue:"blue-PVWoXu5j",secondary:"secondary-PVWoXu5j",ghost:"ghost-PVWoXu5j"}},1538:e=>{e.exports={lightButton:"lightButton-bYDQcOkp",link:"link-bYDQcOkp",ltr:"ltr-bYDQcOkp",rtl:"rtl-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp",content:"content-bYDQcOkp",visuallyHidden:"visuallyHidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp",ellipsisContainer:"ellipsisContainer-bYDQcOkp",textWrapContainer:"textWrapContainer-bYDQcOkp",textWrapWithEllipsis:"textWrapWithEllipsis-bYDQcOkp",slot:"slot-bYDQcOkp",caret:"caret-bYDQcOkp",activeCaret:"activeCaret-bYDQcOkp",xsmall:"xsmall-bYDQcOkp",withStartSlot:"withStartSlot-bYDQcOkp",withEndSlot:"withEndSlot-bYDQcOkp",noContent:"noContent-bYDQcOkp",wrap:"wrap-bYDQcOkp",small:"small-bYDQcOkp",medium:"medium-bYDQcOkp"}},45946:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",slot:"slot-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",withStartSlot:"withStartSlot-D4RPB3ZC",withEndSlot:"withEndSlot-D4RPB3ZC",startSlotWrap:"startSlotWrap-D4RPB3ZC",endSlotWrap:"endSlotWrap-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},88276:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",
readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},73405:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},25549:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},4665:e=>{e.exports={loader:"loader-UL6iwcBa",static:"static-UL6iwcBa",item:"item-UL6iwcBa","tv-button-loader":"tv-button-loader-UL6iwcBa",medium:"medium-UL6iwcBa",small:"small-UL6iwcBa",black:"black-UL6iwcBa",white:"white-UL6iwcBa",gray:"gray-UL6iwcBa",primary:"primary-UL6iwcBa"}},96108:e=>{e.exports={"tablet-normal-breakpoint":"(max-width: 768px)","small-height-breakpoint":"(max-height: 360px)","tablet-small-breakpoint":"(max-width: 440px)"}},10555:e=>{e.exports={wrapper:"wrapper-VB9J73Gf",focused:"focused-VB9J73Gf",readonly:"readonly-VB9J73Gf",disabled:"disabled-VB9J73Gf","size-small":"size-small-VB9J73Gf","size-medium":"size-medium-VB9J73Gf","size-large":"size-large-VB9J73Gf","font-size-small":"font-size-small-VB9J73Gf","font-size-medium":"font-size-medium-VB9J73Gf","font-size-large":"font-size-large-VB9J73Gf","border-none":"border-none-VB9J73Gf",shadow:"shadow-VB9J73Gf","border-thin":"border-thin-VB9J73Gf","border-thick":"border-thick-VB9J73Gf","intent-default":"intent-default-VB9J73Gf","intent-success":"intent-success-VB9J73Gf","intent-warning":"intent-warning-VB9J73Gf","intent-danger":"intent-danger-VB9J73Gf","intent-primary":"intent-primary-VB9J73Gf","corner-top-left":"corner-top-left-VB9J73Gf","corner-top-right":"corner-top-right-VB9J73Gf","corner-bottom-right":"corner-bottom-right-VB9J73Gf","corner-bottom-left":"corner-bottom-left-VB9J73Gf",childrenContainer:"childrenContainer-VB9J73Gf"}},
61796:e=>{e.exports={autocomplete:"autocomplete-uszkUMOz",caret:"caret-uszkUMOz",icon:"icon-uszkUMOz",suggestions:"suggestions-uszkUMOz",suggestion:"suggestion-uszkUMOz",noResults:"noResults-uszkUMOz",selected:"selected-uszkUMOz",opened:"opened-uszkUMOz"}},27061:e=>{e.exports={buttonWrap:"buttonWrap-icygBqe7",desktopSize:"desktopSize-icygBqe7",drawer:"drawer-icygBqe7",menuBox:"menuBox-icygBqe7"}},98993:e=>{e.exports={button:"button-F5dN3ulE",emoji:"emoji-F5dN3ulE",emptySelect:"emptySelect-F5dN3ulE"}},39453:e=>{e.exports={emojiWrap:"emojiWrap-R2CTpmHr",emoji:"emoji-R2CTpmHr",tooltipEmoji:"tooltipEmoji-R2CTpmHr",tooltipEmojiWrap:"tooltipEmojiWrap-R2CTpmHr"}},7567:e=>{e.exports={emojiSelect:"emojiSelect-IY7RpEY6",placeholder:"placeholder-IY7RpEY6"}},49128:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},67248:(e,t,n)=>{"use strict";var o,r,s;function i(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function a(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}n.d(t,{Button:()=>D}),function(e){e.Primary="primary",e.Success="success",e.Default="default",e.Danger="danger"}(o||(o={})),function(e){e.Small="s",e.Medium="m",e.Large="l"}(r||(r={})),function(e){e.Default="default",e.Stroke="stroke"}(s||(s={}));var c=n(50959),u=n(97754),d=n(95604);var p=n(45946),m=n.n(p);const h="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function f(e){const{size:t="medium",variant:n="primary",stretch:o=!1,startSlot:r,endSlot:s,iconOnly:i=!1,className:a,isGrouped:l,cellState:c,disablePositionAdjustment:p=!1,primaryText:f,secondaryText:g,isAnchor:v=!1}=e,y=function(e){return"brand"===e?"black":"blue"===e?"brand":e}(e.color??"brand"),b=function(e){let t="";return 0!==e&&(1&e&&(t=u(t,m()["no-corner-top-left"])),2&e&&(t=u(t,m()["no-corner-top-right"])),4&e&&(t=u(t,m()["no-corner-bottom-right"])),8&e&&(t=u(t,m()["no-corner-bottom-left"]))),t}((0,d.getGroupCellRemoveRoundBorders)(c)),E=i&&(r||s);return u(a,m().button,m()[t],m()[y],m()[n],o&&m().stretch,r&&m().withStartIcon,s&&m().withEndIcon,E&&m().iconOnly,b,l&&m().grouped,l&&!p&&m().adjustPosition,l&&c.isTop&&m().firstRow,l&&c.isLeft&&m().firstCol,f&&g&&m().multilineContent,v&&m().link,h)}function g(e){const{startSlot:t,iconOnly:n,children:o,endSlot:r,primaryText:s,secondaryText:i}=e;if(t&&r&&n)return c.createElement("span",{className:u(m().slot,m().startSlotWrap)},t);const a=n&&(t??r),l=!t&&!r&&!n&&!o&&s&&i;return c.createElement(c.Fragment,null,t&&c.createElement("span",{className:u(m().slot,m().startSlotWrap)},t),o&&!a&&c.createElement("span",{className:m().content},o),r&&c.createElement("span",{className:u(m().slot,m().endSlotWrap)},r),l&&!a&&function(e){return e.primaryText&&e.secondaryText&&c.createElement("div",{className:u(m().textWrap,h)},c.createElement("span",{
className:m().primaryText}," ",e.primaryText," "),"string"==typeof e.secondaryText?c.createElement("span",{className:m().secondaryText}," ",e.secondaryText," "):c.createElement("span",{className:m().secondaryText},c.createElement("span",null,e.secondaryText.firstLine),c.createElement("span",null,e.secondaryText.secondLine)))}(e))}var v=n(34094),y=n(86332),b=n(90186);function E(e,t){return n=>{if(t)return n.preventDefault(),void n.stopPropagation();e?.(n)}}function C(e){const{className:t,color:n,variant:o,size:r,stretch:s,animated:i,iconOnly:a,startSlot:l,endSlot:c,primaryText:u,secondaryText:d,...p}=e;return{...p,...(0,b.filterDataProps)(e),...(0,b.filterAriaProps)(e)}}function x(e){const{reference:t,tooltipText:n,disabled:o,onClick:r,onMouseOver:s,onMouseOut:i,onMouseDown:a,...l}=e,{isGrouped:u,cellState:d,disablePositionAdjustment:p}=(0,c.useContext)(y.ControlGroupContext),m=f({...l,isGrouped:u,cellState:d,disablePositionAdjustment:p}),h=n??(e.primaryText?[e.primaryText,e.secondaryText].join(" "):(0,v.getTextForTooltip)(e.children));return c.createElement("button",{...C(l),"aria-disabled":o,tabIndex:e.tabIndex??(o?-1:0),className:m,ref:t,onClick:E(r,o),onMouseDown:E(a,o),"data-overflow-tooltip-text":h},c.createElement(g,{...l}))}n(49406);function w(e){const{intent:t,size:n,appearance:o,useFullWidth:r,icon:s,...c}=e;return{...c,color:a(t),size:l(n),variant:i(o),stretch:r}}function D(e){return c.createElement(x,{...w(e)})}},27011:(e,t,n)=>{"use strict";function o(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}n.d(t,{isIconOnly:()=>o})},14543:(e,t,n)=>{"use strict";n.d(t,{LightButton:()=>o.LightButton});n(9038);var o=n(15893);n(50959),n(21593),n(66860),n(1538),n(88317);n(49406)},9038:(e,t,n)=>{"use strict";n.d(t,{useLightButtonClasses:()=>c});var o=n(50959),r=n(97754),s=n(17946),i=n(27011),a=n(86332);const l=o.createContext({isInButtonGroup:!1,isGroupPrimary:!1}),c=(e,t,n)=>{const c=(0,o.useContext)(s.CustomBehaviourContext),{className:u,isSelected:d,children:p,showCaret:m,forceDirection:h,iconOnly:f,color:g="gray",variant:v="primary",size:y="medium",enableActiveStateStyles:b=c.enableActiveStateStyles,typography:E,isLink:C=!1,textWrap:x,isPills:w,isActive:D,startSlot:S,endSlot:R}=t,P=e[`typography-${((e,t,n)=>{if(n){const e=n.replace(/^\D+/g,"");return t?`semibold${e}`:n}switch(e){case"xsmall":return t?"semibold14px":"regular14px";case"small":case"medium":return t?"semibold16px":"regular16px";default:return""}})(y,d||w,E||void 0)}`],B=(0,o.useContext)(a.ControlGroupContext),{isInButtonGroup:O,isGroupPrimary:k}=(0,o.useContext)(l);return r(u,e.lightButton,C&&e.link,D&&e.active,d&&e.selected,(0,i.isIconOnly)(p,f)&&e.noContent,!!S&&e.withStartSlot,(m||!!R)&&e.withEndSlot,n&&e.withGrouped,h&&e[h],e[k?"primary":v],e[k?"gray":g],e[y],P,!b&&e.disableActiveStateStyles,B.isGrouped&&e.grouped,x&&e.wrap,O&&e.disableActiveOnTouch,w&&e.pills)}},66860:(e,t,n)=>{"use strict";n.d(t,{LightButtonContent:()=>p});var o=n(50959),r=n(97754),s=n(34094),i=n(27011),a=n(9745),l=n(2948),c=n(1538),u=n.n(c)
;const d=e=>o.createElement(a.Icon,{className:r(u().caret,e&&u().activeCaret),icon:l});function p(e){const{showCaret:t,iconOnly:n,ellipsis:a=!0,textWrap:l,tooltipText:c,children:p,endSlot:m,startSlot:h,isActiveCaret:f}=e;[m,t].filter((e=>!!e));return o.createElement(o.Fragment,null,h&&o.createElement("span",{className:r(u().slot,u().startSlot)},h),!(0,i.isIconOnly)(p,n)&&o.createElement("span",{className:r(u().content,!l&&u().nowrap,"apply-overflow-tooltip","apply-overflow-tooltip--check-children-recursively","apply-overflow-tooltip--allow-text"),"data-overflow-tooltip-text":c??(0,s.getTextForTooltip)(p)},l||a?o.createElement(o.Fragment,null,o.createElement("span",{className:r(!l&&a&&u().ellipsisContainer,l&&u().textWrapContainer,l&&a&&u().textWrapWithEllipsis)},p),o.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},p)):o.createElement(o.Fragment,null,p,o.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},p))),m&&o.createElement("span",{className:r(u().slot,u().endSlot)},m),t&&d(f))}},15893:(e,t,n)=>{"use strict";n.d(t,{LightButton:()=>d});var o=n(50959),r=n(86332),s=n(9038),i=n(66860),a=n(1538),l=n.n(a),c=n(88317),u=n.n(c);function d(e){const{isGrouped:t}=o.useContext(r.ControlGroupContext),{reference:n,className:a,isSelected:c,children:d,iconOnly:p,ellipsis:m,showCaret:h,forceDirection:f,endSlot:g,startSlot:v,color:y,variant:b,size:E,enableActiveStateStyles:C,typography:x,textWrap:w=!1,maxLines:D,style:S={},isPills:R,isActive:P,tooltipText:B,role:O,...k}=e,N=w?D??2:1,T=N>0?{...S,"--ui-lib-light-button-content-max-lines":N}:S;return o.createElement("button",{...k,className:(0,s.useLightButtonClasses)({...u(),...l()},{className:a,isSelected:c,children:d,iconOnly:p,showCaret:h,forceDirection:f,endSlot:g,startSlot:v,color:y,variant:b,size:E,enableActiveStateStyles:C,typography:x,textWrap:w,isPills:R,isActive:P},t),ref:n,style:T,role:O},o.createElement(i.LightButtonContent,{showCaret:h,isActiveCaret:h&&(R||P||c),iconOnly:p,ellipsis:m,textWrap:w,tooltipText:B,endSlot:g,startSlot:v},d))}},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>o});const o=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>y,InputClasses:()=>f});var o=n(50959),r=n(97754),s=n(50151),i=n(38528),a=n(90186),l=n(86332),c=n(95604);var u=n(88276),d=n.n(u);function p(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function m(e,t,n,o){const{removeRoundBorder:s,className:i,intent:a="default",borderStyle:l="thin",size:u,highlight:m,disabled:h,readonly:f,stretch:g,noReadonlyStyles:v,isFocused:y}=e,b=p(s??(0,c.getGroupCellRemoveRoundBorders)(n))
;return r(d().container,d()[`container-${u}`],d()[`intent-${a}`],d()[`border-${l}`],u&&d()[`size-${u}`],b,m&&d()["with-highlight"],h&&d().disabled,f&&!v&&d().readonly,y&&d().focused,g&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],i)}function h(e,t,n){const{highlight:o,highlightRemoveRoundBorder:s}=e;if(!o)return d().highlight;const i=p(s??(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],i)}const f={FontSizeMedium:(0,s.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,s.ensureDefined)(d()["font-size-large"])},g={passive:!1};function v(e,t){const{style:n,id:r,role:s,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:p,onMouseDown:f,onMouseUp:v,onKeyDown:y,onClick:b,tabIndex:E,startSlot:C,middleSlot:x,endSlot:w,onWheel:D,onWheelNoPassive:S=null,size:R,tag:P="span",type:B}=e,{isGrouped:O,cellState:k,disablePositionAdjustment:N=!1}=(0,o.useContext)(l.ControlGroupContext),T=function(e,t=null,n){const r=(0,o.useRef)(null),s=(0,o.useRef)(null),i=(0,o.useCallback)((()=>{if(null===r.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),a=(0,o.useCallback)((()=>{if(null===r.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),l=(0,o.useCallback)((e=>{a(),r.current=e,i()}),[]);return(0,o.useEffect)((()=>(s.current=[e,t,n],i(),a)),[e,t,n]),l}("wheel",S,g),W=P;return o.createElement(W,{type:B,style:n,id:r,role:s,className:m(e,O,k,N),tabIndex:E,ref:(0,i.useMergedRefs)([t,T]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:p,onMouseDown:f,onMouseUp:v,onKeyDown:y,onClick:b,onWheel:D,...(0,a.filterDataProps)(e),...(0,a.filterAriaProps)(e)},C,x,w,o.createElement("span",{className:h(e,k,R)}))}v.displayName="ControlSkeleton";const y=o.forwardRef(v)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>u,EndSlot:()=>c,MiddleSlot:()=>l,StartSlot:()=>a});var o=n(50959),r=n(97754),s=n(73405),i=n.n(s);function a(e){const{className:t,interactive:n=!0,icon:s=!1,children:a}=e;return o.createElement("span",{className:r(i()["inner-slot"],n&&i().interactive,s&&i().icon,t)},a)}function l(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(i()["inner-slot"],i()["inner-middle-slot"],t)},n)}function c(e){const{className:t,interactive:n=!0,icon:s=!1,children:a}=e;return o.createElement("span",{className:r(i()["inner-slot"],n&&i().interactive,s&&i().icon,t)},a)}function u(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(i()["after-slot"],t)},n)}},31261:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>y});var o=n(50959),r=n(97754),s=n(90186),i=n(47201),a=n(48907),l=n(38528),c=n(48027),u=n(29202),d=n(45812),p=n(67029),m=n(78274),h=n(25549),f=n.n(h);function g(e){return!(0,s.isAriaAttribute)(e)&&!(0,s.isDataAttribute)(e)}function v(e){
const{id:t,title:n,role:i,tabIndex:a,placeholder:l,name:c,type:u,value:d,defaultValue:h,draggable:v,autoComplete:y,autoFocus:b,autoCapitalize:E,autoCorrect:C,maxLength:x,min:w,max:D,step:S,pattern:R,inputMode:P,onSelect:B,onFocus:O,onBlur:k,onKeyDown:N,onKeyUp:T,onKeyPress:W,onChange:j,onDragStart:z,size:M="small",className:_,inputClassName:L,disabled:A,readonly:F,containerTabIndex:Z,startSlot:V,endSlot:I,reference:U,containerReference:G,onContainerFocus:Y,...J}=e,Q=(0,s.filterProps)(J,g),H={...(0,s.filterAriaProps)(J),...(0,s.filterDataProps)(J),id:t,title:n,role:i,tabIndex:a,placeholder:l,name:c,type:u,value:d,defaultValue:h,draggable:v,autoComplete:y,autoFocus:b,autoCapitalize:E,autoCorrect:C,maxLength:x,min:w,max:D,step:S,pattern:R,inputMode:P,onSelect:B,onFocus:O,onBlur:k,onKeyDown:N,onKeyUp:T,onKeyPress:W,onChange:j,onDragStart:z};return o.createElement(p.ControlSkeleton,{...Q,disabled:A,readonly:F,tabIndex:Z,className:r(f().container,_),size:M,ref:G,onFocus:Y,startSlot:V,middleSlot:o.createElement(m.MiddleSlot,null,o.createElement("input",{...H,className:r(f().input,f()[`size-${M}`],L,V&&f()["with-start-slot"],I&&f()["with-end-slot"]),disabled:A,readOnly:F,ref:U})),endSlot:I})}function y(e){e=(0,c.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:r=0,onFocus:s,onBlur:p,reference:m,containerReference:h=null}=e,f=(0,o.useRef)(null),g=(0,o.useRef)(null),[y,b]=(0,u.useFocus)(),E=t?void 0:y?-1:r,C=t?void 0:y?r:-1,{isMouseDown:x,handleMouseDown:w,handleMouseUp:D}=(0,d.useIsMouseDown)(),S=(0,i.createSafeMulticastEventHandler)(b.onFocus,(function(e){n&&!x.current&&(0,a.selectAllContent)(e.currentTarget)}),s),R=(0,i.createSafeMulticastEventHandler)(b.onBlur,p),P=(0,o.useCallback)((e=>{f.current=e,m&&("function"==typeof m&&m(e),"object"==typeof m&&(m.current=e))}),[f,m]);return o.createElement(v,{...e,isFocused:y,containerTabIndex:E,tabIndex:C,onContainerFocus:function(e){g.current===e.target&&null!==f.current&&f.current.focus()},onFocus:S,onBlur:R,reference:P,containerReference:(0,l.useMergedRefs)([g,h]),onMouseDown:w,onMouseUp:D})}},17946:(e,t,n)=>{"use strict";n.d(t,{CustomBehaviourContext:()=>o});const o=(0,n(50959).createContext)({enableActiveStateStyles:!0});o.displayName="CustomBehaviourContext"},48027:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>s});var o=n(47201),r=n(29202);function s(e){const{onFocus:t,onBlur:n,intent:s,highlight:i,disabled:a}=e,[l,c]=(0,r.useFocus)(void 0,a),u=(0,o.createSafeMulticastEventHandler)(a?void 0:c.onFocus,t),d=(0,o.createSafeMulticastEventHandler)(a?void 0:c.onBlur,n);return{...e,intent:s||(l?"primary":"default"),highlight:i??l,onFocus:u,onBlur:d}}},29202:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>r});var o=n(50959);function r(e,t){const[n,r]=(0,o.useState)(!1);(0,o.useEffect)((()=>{t&&n&&r(!1)}),[t,n]);const s={onFocus:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!0)}),[e]),onBlur:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!1)}),[e])};return[n,s]}},39416:(e,t,n)=>{"use strict";n.d(t,{useFunctionalRefObject:()=>s})
;var o=n(50959),r=n(43010);function s(e){const t=(0,o.useMemo)((()=>function(e){const t=n=>{e(n),t.current=n};return t.current=null,t}((e=>{a.current(e)}))),[]),n=(0,o.useRef)(null),s=t=>{if(null===t)return i(n.current,t),void(n.current=null);n.current!==e&&(n.current=e,i(n.current,t))},a=(0,o.useRef)(s);return a.current=s,(0,r.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return a.current(t.current),()=>a.current(null)}),[e]),t}function i(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},45812:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>r});var o=n(50959);function r(){const e=(0,o.useRef)(!1),t=(0,o.useCallback)((()=>{e.current=!0}),[e]),n=(0,o.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},38528:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>s});var o=n(50959),r=n(53017);function s(e){return(0,o.useCallback)((0,r.mergeRefs)(e),e)}},27267:(e,t,n)=>{"use strict";function o(e,t,n,o,r){function s(r){if(e>r.timeStamp)return;const s=r.target;void 0!==n&&null!==t&&null!==s&&s.ownerDocument===o&&(t.contains(s)||n(r))}return r.click&&o.addEventListener("click",s,!1),r.mouseDown&&o.addEventListener("mousedown",s,!1),r.touchEnd&&o.addEventListener("touchend",s,!1),r.touchStart&&o.addEventListener("touchstart",s,!1),()=>{o.removeEventListener("click",s,!1),o.removeEventListener("mousedown",s,!1),o.removeEventListener("touchend",s,!1),o.removeEventListener("touchstart",s,!1)}}n.d(t,{addOutsideEventListener:()=>o})},67842:(e,t,n)=>{"use strict";n.d(t,{useResizeObserver:()=>i});var o=n(50959),r=n(43010),s=n(39416);function i(e,t=[]){const{callback:n,ref:i=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),a=(0,o.useRef)(null),l=(0,o.useRef)(n);l.current=n;const c=(0,s.useFunctionalRefObject)(i),u=(0,o.useCallback)((e=>{c(e),null!==a.current&&(a.current.disconnect(),null!==e&&a.current.observe(e))}),[c,a]);return(0,r.useIsomorphicLayoutEffect)((()=>(a.current=new ResizeObserver(((e,t)=>{l.current(e,t)})),c.current&&u(c.current),()=>{a.current?.disconnect()})),[c,...t]),u}},26996:(e,t,n)=>{"use strict";n.d(t,{Loader:()=>l});var o,r=n(50959),s=n(97754),i=n(4665),a=n.n(i);function l(e){const{className:t,size:n="medium",staticPosition:o,color:i="black"}=e,l=s(a().item,a()[i],a()[n]);return r.createElement("span",{className:s(a().loader,o&&a().static,t)},r.createElement("span",{className:l}),r.createElement("span",{className:l}),r.createElement("span",{className:l}))}!function(e){e.Medium="medium",e.Small="small"}(o||(o={}))},90186:(e,t,n)=>{"use strict";function o(e){return s(e,i)}function r(e){return s(e,a)}function s(e,t){const n=Object.entries(e).filter(t),o={};for(const[e,t]of n)o[e]=t;return o}function i(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function a(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>r,filterDataProps:()=>o,filterProps:()=>s,isAriaAttribute:()=>a,isDataAttribute:()=>i})},34094:(e,t,n)=>{"use strict";n.d(t,{getTextForTooltip:()=>i});var o=n(50959);const r=e=>(0,
o.isValidElement)(e)&&Boolean(e.props.children),s=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",i=e=>Array.isArray(e)||(0,o.isValidElement)(e)?o.Children.toArray(e).reduce(((e,t)=>{let n="";return n=(0,o.isValidElement)(t)&&r(t)?i(t.props.children):(0,o.isValidElement)(t)&&!r(t)?"":s(t),e.concat(n)}),"").trim():s(e)},48907:(e,t,n)=>{"use strict";function o(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>o})},47201:(e,t,n)=>{"use strict";function o(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>o})},87896:(e,t,n)=>{"use strict";n.d(t,{createReactRoot:()=>d});var o=n(50959),r=n(32227),s=n(4237);const i=(0,o.createContext)({isOnMobileAppPage:()=>!1,isRtl:!1,locale:"en"});var a=n(84015),l=n(63273);const c={iOs:"old",android:"new",old:"old",new:"new",any:"any"};function u(e){const[t]=(0,o.useState)({isOnMobileAppPage:e=>(0,a.isOnMobileAppPage)(c[e]),isRtl:(0,l.isRtl)(),locale:window.locale});return o.createElement(i.Provider,{value:t},e.children)}function d(e,t,n="legacy"){const i=o.createElement(u,null,e);if("modern"===n){const e=(0,s.createRoot)(t);return e.render(i),{render(t){e.render(o.createElement(u,null,t))},unmount(){e.unmount()}}}return r.render(i,t),{render(e){r.render(o.createElement(u,null,e),t)},unmount(){r.unmountComponentAtNode(t)}}}},24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>r});var o=n(96108);const r={SmallHeight:o["small-height-breakpoint"],TabletSmall:o["tablet-small-breakpoint"],TabletNormal:o["tablet-normal-breakpoint"]}},53680:(e,t,n)=>{"use strict";n.d(t,{Autocomplete:()=>T});var o,r=n(11542),s=n(50959),i=n(97754),a=n.n(i),l=n(10381),c=n(78274),u=n(52778);!function(e){e[e.Enter=13]="Enter",e[e.Space=32]="Space",e[e.Backspace=8]="Backspace",e[e.DownArrow=40]="DownArrow",e[e.UpArrow=38]="UpArrow",e[e.RightArrow=39]="RightArrow",e[e.LeftArrow=37]="LeftArrow",e[e.Escape=27]="Escape",e[e.Tab=9]="Tab"}(o||(o={}));var d=n(42842),p=n(50151),m=n(3343),h=n(39416),f=n(31261),g=n(56570),v=n(173),y=n(9745),b=n(14543),E=n(37285),C=n(82930),x=n(88160),w=n(98993),D=n(94032);function S(e){const{emoji:t,onSelect:o,onClose:i,buttonClassName:l}=e;return s.createElement(C.EmojiPicker,{value:t,onSelect:o,onClose:i,renderButton:e=>function(e,t){const{emoji:o,onClick:i}=e;return s.createElement(b.LightButton,{className:a()(t,w.button,"apply-common-tooltip"),title:r.t(null,void 0,n(4461)),size:"xsmall",color:"gray",variant:"ghost",onClick:i,tabIndex:0,startSlot:o===x.EMPTY_EMOJI?s.createElement(y.Icon,{className:w.emptySelect,icon:D}):s.createElement(E.EmojiItem,{className:w.emoji,emoji:o})})}(e,l),canBeEmpty:!0})}var R=n(7567);const P=g.enabled("advanced_emoji_in_titles");function B(e){const{value:t="",onChange:n,reference:o=null,emojiPicker:r=!1,...i}=e,{emoji:a,emojiLessString:l}=(0,s.useMemo)((()=>(0,v.separateEmoji)(t)),[t]),c=(0,h.useFunctionalRefObject)(o);return P&&r?s.createElement(f.InputControl,{...i,reference:c,value:l,onChange:function(e){n?.(a+e.currentTarget.value)},
onKeyDown:function(t){if(e.onKeyDown?.(t),t.defaultPrevented)return;const{selectionStart:o,selectionEnd:r}=(0,p.ensureNotNull)(c.current);0===o&&0===r&&a&&8===(0,m.hashFromEvent)(t)&&(t.preventDefault(),n?.(l))},startSlot:s.createElement(S,{emoji:a,onSelect:function(e){n?.(e+l)},onClose:function(){c.current?.focus()},buttonClassName:R.emojiSelect})}):s.createElement(f.InputControl,{...i,value:t,reference:o,onChange:function(e){n?.(e.currentTarget.value)}})}var O=n(56127),k=n(61796);function N(e,t){return""===e||-1!==t.toLowerCase().indexOf(e.toLowerCase())}class T extends s.PureComponent{constructor(e){if(super(e),this._containerInputElement=null,this._raf=null,this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleMeasure=()=>{if(this.state.isMeasureValid||!this.props.suggestionsInPortal||!this._containerInputElement)return;const{bottom:e,left:t,width:n}=this._containerInputElement.getBoundingClientRect();this.setState({appearingWidth:n,appearingPosition:{x:t,y:e},isMeasureValid:!0})},this._setInputRef=e=>{e&&(this._inputElement=e,this.props.setupHTMLInput&&this.props.setupHTMLInput(e),this._inputElement.addEventListener("keyup",this._handleKeyUpEnter))},this._setContainerInputRef=e=>{this._containerInputElement=e},this._handleCaretClick=()=>{this.state.isOpened?(this._close(),this.props.preventOnFocusOpen&&this._focus()):this.props.preventOnFocusOpen?this._open():this._focus()},this._handleOutsideClick=()=>{const{allowUserDefinedValues:e,value:t,onChange:n}=this.props,{queryValue:o}=this.state;e?n&&o!==t&&n(o):this.setState(this._valueToQuery(t)),this._close()},this._handleFocus=e=>{this.props.preventOnFocusOpen||this._open(),this.props.onFocus&&this.props.onFocus(e)},this._handleChange=e=>{const{preventSearchOnEmptyQuery:t,allowUserDefinedValues:n,onChange:o,onSuggestionsOpen:r,onSuggestionsClose:s}=this.props;if(t&&""===e)this.setState({queryValue:e,isOpened:!1,active:void 0}),s&&s();else{const t=this._suggestions(e),o=Object.keys(t).length>0;this.setState({queryValue:e,isOpened:o,active:n?void 0:this._getActiveKeyByValue(e)}),o&&r&&r()}n&&o&&o(e)},this._handleItemClick=e=>{const t=e.currentTarget.id;this.setState({queryValue:W(this.props.source)[t]}),this.props.onChange&&this.props.onChange(t),this._close()},this._handleKeyDown=e=>{if(-1===[o.DownArrow,o.UpArrow,o.Enter,o.Escape].indexOf(e.which))return;const{allowUserDefinedValues:t,value:n,onChange:r,onSuggestionsOpen:s}=this.props,{active:i,isOpened:a,queryValue:l}=this.state;a&&(e.preventDefault(),e.stopPropagation());const c=this._suggestions(l);switch(e.which){case o.DownArrow:case o.UpArrow:const u=Object.keys(c);if(!a&&u.length&&e.which===o.DownArrow){this.setState({isOpened:!0,active:u[0]}),s&&s();break}let d;if(void 0===i){if(e.which===o.UpArrow){this._close();break}d=0}else d=u.indexOf(i)+(e.which===o.UpArrow?-1:1);d<0&&(d=0),d>u.length-1&&(d=u.length-1);const p=u[d];this.setState({active:p})
;const m=document.getElementById(p);m&&this._scrollIfNotVisible(m,this._suggestionsElement);break;case o.Escape:this._close(),a||this._blur();break;case o.Enter:let h=i;t&&(a&&h?this.setState(this._valueToQuery(h)):h=l),void 0!==h&&(this._close(),a||this._blur(),h!==n?r&&r(h):this.setState(this._valueToQuery(h)))}},this._setSuggestionsRef=e=>{e&&(this._suggestionsElement=e)},this._scrollIfNotVisible=(e,t)=>{const n=t.scrollTop,o=t.scrollTop+t.clientHeight,r=e.offsetTop,s=r+e.clientHeight;r<=n?e.scrollIntoView(!0):s>=o&&e.scrollIntoView(!1)},!(e=>Array.isArray(e.source)||!e.allowUserDefinedValues)(e))throw new Error("allowUserDefinedProps === true cay only be used if source is array");this.state={valueFromProps:e.value,isOpened:!1,active:e.value,queryValue:W(e.source)[e.value]||(e.allowUserDefinedValues?e.value:"")}}componentDidMount(){this.props.suggestionsInPortal&&window.addEventListener("resize",this._resize)}componentDidUpdate(){this.state.isOpened&&this._handleMeasure()}componentWillUnmount(){this._inputElement&&this._inputElement.removeEventListener("keyup",this._handleKeyUpEnter),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),window.removeEventListener("resize",this._resize)}render(){const{emojiPicker:e}=this.props;return s.createElement(u.OutsideEvent,{handler:this._handleOutsideClick,click:!0},(t=>s.createElement("div",{className:i(k.autocomplete,"js-dialog-skip-escape"),ref:t},s.createElement(B,{id:this.props.id,name:this.props.name,endSlot:Object.keys(this._suggestions(this.state.queryValue)).length?s.createElement(c.EndSlot,null,s.createElement("span",{className:k.caret,onClick:this._handleCaretClick,tabIndex:-1},s.createElement(l.ToolWidgetCaret,{className:k.icon,dropped:this.state.isOpened}))):void 0,maxLength:this.props.maxLength,reference:this._setInputRef,containerReference:this._setContainerInputRef,stretch:!0,placeholder:this.props.placeholder,value:this.state.queryValue,intent:this.props.error?"danger":void 0,onChange:this._handleChange,onFocus:this._handleFocus,onBlur:this.props.onBlur,onMouseOver:this.props.onMouseOver,onMouseOut:this.props.onMouseOut,onKeyDown:this._handleKeyDown,autoComplete:"off",size:this.props.size,emojiPicker:e}),this._renderSuggestions())))}static getDerivedStateFromProps(e,t){const{allowUserDefinedValues:n,value:o,source:r}=e;if(o===t.valueFromProps&&t.isOpened)return null;const s=n?o:""===o?"":W(r)[o]||t.queryValue;return{...t,valueFromProps:o,active:o,queryValue:s}}_renderSuggestions(){return this.props.suggestionsInPortal?this.state.isOpened?this._renderPortalSuggestions():null:this._renderSuggestionsItems()}_renderPortalSuggestions(){return s.createElement(d.Portal,null,this._renderSuggestionsItems())}_focus(){this._inputElement.focus()}_blur(){this._inputElement.blur()}_open(){const{onSuggestionsOpen:e}=this.props;this._focus(),this.setState({isOpened:!0,active:this.props.value}),e&&e()}_close(){const{onSuggestionsClose:e}=this.props;this.setState({isOpened:!1,active:void 0}),e&&e()}_suggestions(e){
const{filter:t=N}=this.props,n=W(this.props.source),o={};return Object.keys(n).filter((o=>t(e,n[o]))).forEach((e=>o[e]=n[e])),o}_renderSuggestionsItems(){const e=this._suggestions(this.state.queryValue),t=Object.keys(e).map((t=>{const n=i(k.suggestion,this.state.active===t&&k.selected);return s.createElement("li",{id:t,key:t,className:n,onClick:this._handleItemClick},s.createElement(O.LeadingEmojiText,{text:e[t]}))})),o=s.createElement("li",{className:k.noResults},r.t(null,void 0,n(85888)));if(!t.length&&this.props.noEmptyText)return null;const{appearingPosition:a,appearingWidth:l}=this.state;return s.createElement("ul",{className:i(k.suggestions,this.state.isOpened&&k.opened),ref:this._setSuggestionsRef,style:{left:a&&a.x,top:a&&a.y,width:l&&l}},t.length?t:o)}_handleKeyUpEnter(e){e.which===o.Enter&&e.stopImmediatePropagation()}_getActiveKeyByValue(e){const{filter:t=N}=this.props,n=this._suggestions(e),o=Object.keys(n);for(const r of o)if(t(e,n[r]))return r;return o[0]}_valueToQuery(e){return{queryValue:W(this.props.source)[e]||""}}}function W(e){let t={};return Array.isArray(e)?e.forEach((e=>{t[e]=e})):t=e,t}},16684:(e,t,n)=>{"use strict";n.d(t,{useAutoSelect:()=>s});var o=n(50959),r=n(49483);function s(){const e=(0,o.useRef)(null);return(0,o.useLayoutEffect)((()=>{r.CheckMobile.iOS()||e.current&&(e.current.focus(),e.current.select())}),[]),e}},82930:(e,t,n)=>{"use strict";n.d(t,{EmojiPicker:()=>O});var o=n(50959),r=n(56840),s=n(38297),i=n(43790),a=n(173);var l=n(20520),c=n(37558),u=n(41590),d=n(27317),p=n(3343),m=n(40173),h=n(90692);function f(e){!function(e,t){(0,o.useEffect)((()=>{const n=t||document;return n.addEventListener("scroll",e),()=>n.removeEventListener("scroll",e)}),[e])}(e,document)}var g=n(78135),v=n(24437),y=n(97754),b=n.n(y),E=n(10555);function C(e){const{children:t,highlight:n,disabled:r,reference:s,...i}=e,a=n?"primary":"default";return o.createElement("div",{...i,ref:s,className:b()(E.wrapper,E[`intent-${a}`],E["border-thin"],E["size-medium"],n&&E.highlight,n&&E.focused,r&&E.disabled),"data-role":"button"},o.createElement("div",{className:b()(E.childrenContainer,r&&E.disabled)},t),n&&o.createElement("span",{className:E.shadow}))}var x=n(88160),w=n(27061);const D=()=>null,S=(0,m.mergeThemes)(d.DEFAULT_MENU_THEME,{menuBox:w.menuBox}),R=378,P=18,B=200;function O(e){const{value:t,disabled:n,onSelect:i,onClose:d,canBeEmpty:m,renderButton:y=k}=e,b=(0,o.useRef)(null),{current:E}=(0,o.useRef)((C=t,r.getJSON("RecentlyUsedEmojis",[C]).filter((e=>e!==x.EMPTY_EMOJI))));var C;const O=(0,o.useRef)(null),[N,T]=(0,o.useState)(E),[W,j]=(0,o.useState)(!1),z=(0,o.useCallback)((()=>{j(!1),d?.()}),[d]),M=(0,o.useRef)(0);f((0,o.useCallback)((()=>{Date.now()-M.current<B||z()}),[z]));const _=(0,o.useCallback)((e=>{if(e!==x.EMPTY_EMOJI){const t=Array.from(new Set([e,...N])).slice(0,P);r.setJSON("RecentlyUsedEmojis",t),T(t)}i(e),z()}),[N,i]),L=(0,o.useMemo)((()=>m?[x.EMPTY_EMOJI,...N].slice(0,P):N),[N,m]),A=(F=L,(0,o.useMemo)((()=>{const e=(0,a.emojiGroups)();return e[0].emojis=F,e}),[F]));var F
;return o.createElement(o.Fragment,null,o.createElement("div",{ref:b,className:w.buttonWrap},y({emoji:t,isOpened:W,disabled:n,onClick:function(){if(W)return void z();n||(j(!0),M.current=Date.now())}})),o.createElement(h.MatchMedia,{rule:v.DialogBreakpoints.TabletSmall},(e=>W&&o.createElement(c.DrawerManager,null,e?o.createElement(u.Drawer,{className:w.drawer,position:"Bottom",onClose:z},o.createElement(s.EmojiList,{emojis:A,onSelect:_,height:R})):o.createElement(l.PopupMenu,{theme:S,onKeyDown:V,isOpened:!0,position:(0,g.getPopupPositioner)(b.current,{horizontalDropDirection:g.HorizontalDropDirection.FromLeftToRight,horizontalAttachEdge:g.HorizontalAttachEdge.Left}),closeOnClickOutside:!1,onClickOutside:I,onClose:D,controller:O,onOpen:Z,tabIndex:-1},o.createElement(s.EmojiList,{className:w.desktopSize,emojis:A,onSelect:_,height:R}))))));function Z(){O.current?.focus()}function V(e){27===(0,p.hashFromEvent)(e)&&(e.preventDefault(),e.stopPropagation(),z())}function I(e){const t=e.target;t instanceof Node&&b.current?.contains(t)||z()}}function k(e){const{emoji:t,isOpened:n,disabled:r,onClick:s}=e;return o.createElement(C,{highlight:n,disabled:r,"data-name":"emoji-picker"},o.createElement(i.EmojiWrap,{emoji:t,onClick:s}))}},56127:(e,t,n)=>{"use strict";n.d(t,{LeadingEmojiText:()=>a});var o=n(50959),r=n(63472),s=n(37285),i=n(39453);function a(e){const{text:t,textRender:n,firstSegmentOnly:a=!1}=e,{leadingEmoji:l,processedText:c}=(0,o.useMemo)((()=>(0,r.processTextWithLeadingEmoji)({text:t,textRender:n,firstSegmentOnly:a})),[t,n,a]);return l?o.createElement(o.Fragment,null,o.createElement("span",{className:i.emojiWrap}," ",o.createElement(s.EmojiItem,{className:i.emoji,emoji:l})),""!==c&&o.createElement(o.Fragment,null," ",c)):o.createElement(o.Fragment,null,c)}},63472:(e,t,n)=>{"use strict";n.d(t,{getLeadingEmojiHtml:()=>d,processTextWithLeadingEmoji:()=>u});var o=n(91682),r=n(173),s=n(88160),i=n(56570),a=n(19365),l=n(39453);const c=i.enabled("advanced_emoji_in_titles");function u(e){const{text:t,textRender:n=e=>e,firstSegmentOnly:i=!1}=e,a=(0,o.getFirstSegmentOrCodePointString)(t),l=null!==a&&(0,r.isSupportedEmoji)(a)?a:s.EMPTY_EMOJI,u=i?a||"":t;if(!c||l===s.EMPTY_EMOJI)return{leadingEmoji:"",processedText:n(u)};return{leadingEmoji:l,processedText:n(u.replace(l,""))}}function d(e){const{processedText:t,leadingEmoji:n}=u({text:e}),r=(0,o.htmlEscape)(t);if(!n)return r;return`${function(e){const t=(0,a.getTwemojiUrl)(e,"png");return`<span class=${l.tooltipEmojiWrap}>&nbsp<img class=${l.tooltipEmoji} src=${t} decoding="async" width="12" height="12" alt="" draggable="false"/></span>`}(n)}&nbsp;${r}`}},78036:(e,t,n)=>{"use strict";n.d(t,{useEnsuredContext:()=>s});var o=n(50959),r=n(50151);function s(e){return(0,r.ensureNotNull)((0,o.useContext)(e))}},29006:(e,t,n)=>{"use strict";n.d(t,{useResizeObserver:()=>o.useResizeObserver});var o=n(67842)},20520:(e,t,n)=>{"use strict";n.d(t,{PopupMenu:()=>p});var o=n(50959),r=n(32227),s=n(88987),i=n(42842),a=n(27317),l=n(29197);const c=o.createContext(void 0);var u=n(36383)
;const d=o.createContext({setMenuMaxWidth:!1});function p(e){const{controller:t,children:n,isOpened:p,closeOnClickOutside:m=!0,doNotCloseOn:h,onClickOutside:f,onClose:g,onKeyboardClose:v,"data-name":y="popup-menu-container",...b}=e,E=(0,o.useContext)(l.CloseDelegateContext),C=o.useContext(d),x=(0,o.useContext)(c),w=(0,u.useOutsideEvent)({handler:function(e){f&&f(e);if(!m)return;const t=(0,s.default)(h)?h():null==h?[]:[h];if(t.length>0&&e.target instanceof Node)for(const n of t){const t=r.findDOMNode(n);if(t instanceof Node&&t.contains(e.target))return}g()},mouseDown:!0,touchStart:!0});return p?o.createElement(i.Portal,{top:"0",left:"0",right:"0",bottom:"0",pointerEvents:"none"},o.createElement("span",{ref:w,style:{pointerEvents:"auto"}},o.createElement(a.Menu,{...b,onClose:g,onKeyboardClose:v,onScroll:function(t){const{onScroll:n}=e;n&&n(t)},customCloseDelegate:E,customRemeasureDelegate:x,ref:t,"data-name":y,limitMaxWidth:C.setMenuMaxWidth,"data-tooltip-show-on-focus":"true"},n))):null}},10381:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>l});var o=n(50959),r=n(97754),s=n(9745),i=n(49128),a=n(578);function l(e){const{dropped:t,className:n}=e;return o.createElement(s.Icon,{className:r(n,i.icon,{[i.dropped]:t}),icon:a})}},4237:(e,t,n)=>{"use strict";var o=n(32227);t.createRoot=o.createRoot,o.hydrateRoot},78135:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>r,HorizontalDropDirection:()=>i,VerticalAttachEdge:()=>o,VerticalDropDirection:()=>s,getPopupPositioner:()=>c});var o,r,s,i,a=n(50151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(o||(o={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(r||(r={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(s||(s={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(i||(i={}));const l={verticalAttachEdge:o.Bottom,horizontalAttachEdge:r.Left,verticalDropDirection:s.FromTopToBottom,horizontalDropDirection:i.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return n=>{const{contentWidth:c,contentHeight:u,availableHeight:d}=n,p=(0,a.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:m=l.horizontalAttachEdge,horizontalDropDirection:h=l.horizontalDropDirection,horizontalMargin:f=l.horizontalMargin,verticalMargin:g=l.verticalMargin,matchButtonAndListboxWidths:v=l.matchButtonAndListboxWidths}=t;let y=t.verticalAttachEdge??l.verticalAttachEdge,b=t.verticalDropDirection??l.verticalDropDirection;y===o.AutoStrict&&(d<p.y+p.height+g+u?(y=o.Top,b=s.FromBottomToTop):(y=o.Bottom,b=s.FromTopToBottom));const E=y===o.Top?-1*g:g,C=m===r.Right?p.right:p.left,x=y===o.Top?p.top:p.bottom,w={x:C-(h===i.FromRightToLeft?c:0)+f,y:x-(b===s.FromBottomToTop?u:0)+E};return v&&(w.overrideWidth=p.width),w}}},2948:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.92 7.83 9 12.29l5.08-4.46-1-1.13L9 10.29l-4.09-3.6-.99 1.14Z"/></svg>'},94032:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M8 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM11 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2ZM6.2 10A3 3 0 0 0 9 12a3 3 0 0 0 2.8-2l.95.34A4 4 0 0 1 9 13a4 4 0 0 1-3.75-2.66L6.2 10Z"/><path fill="currentColor" fill-rule="evenodd" d="M1 9a8 8 0 1 1 16 0A8 8 0 0 1 1 9Zm1 0a7 7 0 1 1 14 0A7 7 0 0 1 2 9Z"/></svg>'},578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'}}]);