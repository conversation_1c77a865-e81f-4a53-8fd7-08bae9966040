### How translation import & export works?

We use Google Sheets as the UI for translators. We use `csv` files to transfer data between the Google Sheets table and `next-intl` json files.

The translation process looks like this:

- a developer runs a script to export json messages to a csv file
- the developer imports the csv file into the Google Sheets sheet
- a translator makes updates to the Google Sheets sheet
- the developer exports the sheet into a csv file
- the developer runs a script to import the csv file into the json messages file

### Script

Our script has two commands: `import` and `export`.

To run the script:

```sh
$ yarn tsx src/scripts/i18n.ts
```

Then, the script asks you to select your command.

### Command `export`

It reads json files and creates a csv file called `i18n_export.csv` in the project root.

### Command `import`

It reads a csv file `i18n_import.csv` and imports translations from it into json files.

### Translation update process

Before exporting translations into the sheet, you should first import translations from the sheet to avoid overriding changes in the sheet.

You can follow the following steps:

- open sheet - https://docs.google.com/spreadsheets/d/1P-3z1Qn9VzdfI29_m0gHc3ocDMjavjc8P2t3fkqtxqk/edit?gid=0#gid=0
- select translations including column headers
- File > Download > Comma Separated Values
- save file under `i18n_import.csv`
- run `import` script
- review & commit changes
- run `export` script
- File > Import
- upload `i18n_export.csv`
- select these options

```toml
import_location = "Insert new sheet(s)"
convert_text_to_numbers = false
```

- open imported sheet
- copy content into the first sheet
- delete imported sheet
