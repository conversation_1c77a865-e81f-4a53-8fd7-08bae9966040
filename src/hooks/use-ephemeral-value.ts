import { useEffect, useState } from 'react';

const DEFAULT_EPHEMERAL_VALUE = 2_000;

export const useEphemeralValue = <T>(defaultValue?: T, ms = DEFAULT_EPHEMERAL_VALUE) => {
  const [value, setValue] = useState<T | undefined>(defaultValue);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setValue(undefined);
    }, ms);

    return () => {
      clearTimeout(timeout);
    };
  }, [ms, value]);

  return [value, setValue] as const;
};
