/* eslint-disable no-console */
import { runCommandExport } from '@/scripts/i18n/export';
import { runCommandImport } from '@/scripts/i18n/import';
import { askUserToSelectOption } from '@/scripts/i18n/utils';

async function run() {
  const command = await askUserToSelectOption(['export', 'import'], 'What would you like to do?');
  if (command === 'export') {
    await runCommandExport();
  }
  if (command === 'import') {
    await runCommandImport();
  }
}

run().catch((error: unknown) => {
  console.error('error while running script', error);
});
