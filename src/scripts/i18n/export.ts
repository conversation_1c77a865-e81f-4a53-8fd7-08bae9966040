import fs from 'fs/promises';
import path from 'path';

import chalk from 'chalk';
import * as csv from 'csv/sync';

import { config } from '@/scripts/i18n/config';
import {
  findRootPath,
  getAllTranslationWithKeys,
  loadTranslationsForLocale,
  log,
  numberComparator,
} from '@/scripts/i18n/utils';

export async function runCommandExport() {
  const rootPath = await findRootPath();

  log('exporting');
  const localeFiles = await Promise.all(
    config.locales.map(async (locale) => ({ locale, translations: await loadTranslationsForLocale(locale) })),
  );

  const translations = getAllTranslationWithKeys(localeFiles);
  const nonSourceCsvLocales = config.locales.filter((locale) => locale !== 'en');

  const csvRows = translations
    .sort(
      numberComparator((x) => {
        const emptyLanguages = config.locales.map((locale) => x.value.otherLanguages[locale]).filter((x) => !x);
        return emptyLanguages.length;
      }),
    )
    .map((x) => [
      x.key,
      x.value.originalLanguage,
      ...nonSourceCsvLocales.map((locale) => x.value.otherLanguages[locale]),
    ]);

  const csvFileString = csv.stringify([['key', 'en', ...nonSourceCsvLocales], ...csvRows], {
    quoted: true,
    quoted_empty: true,
  });

  await fs.writeFile(path.join(rootPath, 'i18n_export.csv'), csvFileString, {
    encoding: 'utf8',
  });

  log(`exported translations to ${chalk.green('i18n_export.csv')}.`);
  log(`now, you can import the csv file into Google Sheets`);
  log(`see ${chalk.green('docs/i18n.md')} for more info`);
}
