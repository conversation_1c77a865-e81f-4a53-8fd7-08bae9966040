import fs from 'fs/promises';
import path from 'path';

import * as csv from 'csv/sync';
import { execa } from 'execa';
import get from 'lodash.get';
import set from 'lodash.set';
import { z } from 'zod';

import { config } from '@/scripts/i18n/config';
import { findRootPath, loadTranslationsForLocale, log } from '@/scripts/i18n/utils';

async function importTranslations(csvRows: Record<string, string>[]) {
  const rootPath = await findRootPath();
  for (const locale of config.locales) {
    const localeJson = await loadTranslationsForLocale(locale);

    for (const row of csvRows) {
      const translationKey = z.string().parse(row.key);
      const importedValue = row[locale] ?? '';

      const currentValue = get(localeJson, translationKey) ?? '';

      const isChange = currentValue !== importedValue;

      if (isChange && importedValue) {
        log(
          `importing changed key: ${JSON.stringify(
            translationKey,
          )} (original value: ${JSON.stringify(currentValue)}, imported value: ${JSON.stringify(importedValue)})`,
        );
        set(localeJson, translationKey, importedValue);
      }
    }
    const jsonFilePath = path.join(rootPath, 'messages', locale + '.json');
    const INDENT = 3;
    await fs.writeFile(jsonFilePath, JSON.stringify(localeJson, null, INDENT), { flush: true });
    await execa({ stdio: 'inherit' })`prettier --write ${jsonFilePath}`;
  }
}

export async function runCommandImport() {
  const rootPath = await findRootPath();

  const csvRows = await fs
    .readFile(path.join(rootPath, 'i18n_import.csv'))
    .then(
      (x) =>
        csv.parse(x.toString('utf-8'), {
          columns: true,
          skipEmptyLines: true,
        }) as unknown,
    )
    .then((result) => z.array(z.record(z.string())).parse(result));

  await importTranslations(csvRows);
}
