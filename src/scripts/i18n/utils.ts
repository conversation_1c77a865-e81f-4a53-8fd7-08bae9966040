import fs from 'fs/promises';
import path from 'path';

import chalk from 'chalk';
import enquirer from 'enquirer';
import get from 'lodash.get';
import { z } from 'zod';

import type { SupportedLocale } from '@/scripts/i18n/config';

export async function findRootPath() {
  const cwd = process.cwd();

  const files = await fs.readdir(cwd);

  if (files.some((x) => x === 'package.json')) {
    return cwd;
  }

  throw new Error('Please run this command in the root path.');
}

export async function loadTranslationsForLocale(locale: SupportedLocale) {
  const rootPath = await findRootPath();

  return await fs
    .readFile(path.join(rootPath, 'messages', locale + '.json'))
    .then((x) => x.toString('utf8'))
    .catch(() => '{}')
    .then((x) => JSON.parse(x) as unknown)
    .then((x) => z.record(z.unknown()).parse(x));
}

export interface TranslationEntry {
  key: string;
  value: {
    originalLanguage: string;
    otherLanguages: Record<string, string>;
  };
}

export function getAllTranslationWithKeys(
  localeFiles: { locale: SupportedLocale; translations: unknown }[],
): TranslationEntry[] {
  const sourceLocale = localeFiles.find((x) => x.locale === 'en');
  const otherLocales = localeFiles.filter((x) => x.locale !== 'en');

  const workQueue = [{ sourceTranslations: sourceLocale?.translations, path: '' }];
  const translations: TranslationEntry[] = [];

  while (workQueue.length > 0) {
    const nextItem = workQueue.shift();
    if (!nextItem) break;

    const { sourceTranslations } = nextItem;

    if (sourceTranslations == null) continue;

    if (typeof sourceTranslations === 'object') {
      for (const [key, value] of Object.entries(sourceTranslations)) {
        workQueue.push({
          sourceTranslations: value,
          path: [nextItem.path, key].filter((x) => !!x).join('.'),
        });
      }
    } else {
      const otherLanguages = Object.fromEntries(
        otherLocales.map((x) => {
          const value = z.string().parse(get(x.translations, nextItem.path) ?? '');
          return [x.locale, value] as const;
        }),
      );

      translations.push({
        key: nextItem.path,
        value: {
          originalLanguage: z.string().parse(nextItem.sourceTranslations),
          otherLanguages,
        },
      });
    }
  }

  return translations;
}

export function numberComparator<T>(map: (x: T) => number): (p: T, n: T) => number {
  return (p: T, n: T): number => {
    const pNumber = map(p);
    const nNumber = map(n);

    return pNumber - nNumber;
  };
}

export async function askUserToSelectOption(options: string[], message: string) {
  const { option } = await enquirer
    .prompt({
      name: 'option',
      type: 'autocomplete',
      message,
      choices: options,
    })
    .then((x) => z.object({ option: z.string() }).parse(x));

  return option;
}

export function log(...args: unknown[]) {
  // eslint-disable-next-line no-console
  console.log(chalk.blue('[log]'), ...args);
}
