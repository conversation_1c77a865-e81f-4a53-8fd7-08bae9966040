'use client';
import { useTranslations } from 'next-intl';

import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { Button } from '@/components/ui/button';
import { useEphemeralValue } from '@/hooks/use-ephemeral-value';
import { copyToClipboard } from '@/lib/copy-to-clipboard';
import { getErrorMessage } from '@/lib/get-error-message';

export const Share = ({ link }: { link: string }) => {
  const t = useTranslations('invite');

  const [tooltip, setTooltip] = useEphemeralValue<string>();

  const handleShare = async () => {
    try {
      await copyToClipboard(link);
      setTooltip(t('link-copied'));
    } catch (error: unknown) {
      setTooltip(getErrorMessage(error) ?? '');
    }
  };

  return (
    <Tooltip open={!!tooltip}>
      <TooltipTrigger asChild>
        <Button className="w-full" variant="invert" onClick={handleShare}>
          {t('share')}
        </Button>
      </TooltipTrigger>
      <TooltipContent>{tooltip}</TooltipContent>
    </Tooltip>
  );
};
