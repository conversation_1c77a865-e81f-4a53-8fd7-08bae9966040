'use client';

import { Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import type { FC } from 'react';

import { ClearButton } from '@/components/clear-button';
import { DetailListHeader } from '@/components/data-list/detail-list-header';
import { Input } from '@/components/form/input';
import { InputAdornment } from '@/components/form/input-adornment';
import { Card } from '@/components/ui/card';
import { MyAssetsDetailVirtualList } from '@/module/manual-trading/my-assets-detail/my-assets-detail-virtual-list';

export const MyAssetsSearchContent: FC = () => {
  const t = useTranslations();
  const [searchString, setSearchString] = useQueryState('searchString');

  return (
    <Card className="mx-auto flex max-w-(--breakpoint-lg) flex-col gap-y-2 lg:mx-auto" variant="area">
      <DetailListHeader title={t('common.search')} titleClassName="md:ml-0 text-display-m" />

      <div className="relative flex w-full">
        <Input
          className="rounded-full pr-6"
          icon={<Search className="size-3 text-text-active-secondary" />}
          value={searchString ?? ''}
          wrapperClassName="flex grow"
          onChange={(event) => void setSearchString(event.target.value.trim())}
        />

        {searchString ? (
          <InputAdornment className="absolute right-2 z-10">
            <ClearButton onClear={() => void setSearchString(null)} />
          </InputAdornment>
        ) : null}
      </div>

      {/* TODO: workaround so we don't have to change layout*/}
      {/* should be fixed with layout refactoring and respecting full height*/}
      <div className="h-[calc(100vh-270px)] sm:h-[calc(100vh-400px)] lg:h-[calc(100vh-300px)]">
        <MyAssetsDetailVirtualList />
      </div>
    </Card>
  );
};
