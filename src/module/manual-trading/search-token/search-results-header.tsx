import { useTranslations } from 'next-intl';
import React from 'react';

import { cn } from '@/lib/utils';

interface Props {
  className?: string;
}

export const SearchResultsHeader: React.FC<Props> = ({ className }) => {
  const t = useTranslations('common');

  return (
    <div
      className={cn(
        'grid w-full grid-cols-[1fr_1fr] items-center pr-2 text-md font-medium text-text-secondary  md:grid-cols-[6fr_2fr_2fr_2fr] md:gap-0',
        className
      )}
    >
      <div className="flex justify-start">{t('token')}</div>
      <div className="hidden justify-end md:flex">{t('price')}</div>
      <div className="hidden justify-end md:flex">{t('24h')}</div>
      <div className="relative left-2 flex justify-end">
        {t('1h-volume-world')}
      </div>
    </div>
  );
};
