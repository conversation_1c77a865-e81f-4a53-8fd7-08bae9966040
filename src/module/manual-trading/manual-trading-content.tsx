import { Card } from '@/components/ui/card';
import { HotTokens } from '@/module/manual-trading/hot-tokens/hot-tokens';
import { LastTransactions } from '@/module/manual-trading/last-transactions/last-transactions';
import { MyAssets } from '@/module/manual-trading/my-assets/my-assets';
import { PortfolioOverviewCard } from '@/module/manual-trading/portfolio-overview/portfolio-overview-card';
import { SearchToken } from '@/module/manual-trading/search-token/search-token';

export const ManualTradingContent = () => (
    <div className="grid grid-cols-12 gap-y-5 sm:gap-2">
      <div className="contents flex-col gap-2 xl:col-span-4 xl:flex">
        <div className="order-first col-span-12 grid grid-cols-1 gap-5 sm:h-fit sm:grid-cols-2 sm:gap-2 xl:grid-cols-1">
          <PortfolioOverviewCard />
          <SearchToken />
        </div>
        <Card className="order-2 col-span-12 bg-transparent p-0 sm:bg-surface-area sm:p-3">
          <LastTransactions />
        </Card>
      </div>
      <div className="contents h-fit flex-col gap-5 sm:gap-2 xl:col-span-8 xl:flex">
        <Card className="order-1 col-span-12 bg-transparent p-0 sm:bg-surface-area sm:p-3">
          <MyAssets />
        </Card>
        <Card className="order-last col-span-12 bg-transparent p-0 sm:bg-surface-area sm:p-3">
          <HotTokens />
        </Card>
      </div>
    </div>
  );
