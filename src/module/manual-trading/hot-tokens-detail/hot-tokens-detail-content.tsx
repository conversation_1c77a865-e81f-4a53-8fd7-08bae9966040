'use client';

import { useQueryClient } from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useDebounceCallback } from 'usehooks-ts';

import { DetailListHeader } from '@/components/data-list/detail-list-header';
import { DetailListContainer } from '@/components/detail-list-container';
import { QuickBuyInput } from '@/components/form/quick-buy-input';
import { toast } from '@/components/toast';
import { Card } from '@/components/ui/card';
import {
  type Chain,
  getGetUserQueryKey,
  type QuickBuyTokenV2MutationResult,
  useGetUser,
  useQuickBuyTokenV2,
  useSetQuickBuyAmount,
} from '@/lib/api';
import { ResultDialog } from '@/module/token-detail/manual-trading/result-dialog/result-dialog';

import { HotTokensVirtualList } from './hot-tokens-virtual-list';

export const HotTokensDetailContent: React.FC = () => {
  const queryClient = useQueryClient();
  const t = useTranslations('manual-trading');

  const { data: user } = useGetUser();

  const [buyTxHashes, setBuyTxHashes] = useState<string[]>([]);
  const [buyToken, setBuyToken] = useState<{ address: string; chain: Chain }>();

  const { mutate: quickBuyToken, isPending, error } = useQuickBuyTokenV2<QuickBuyTokenV2MutationResult, AxiosError>();

  const { mutate: setQuickBuyAmount } = useSetQuickBuyAmount({
    mutation: {
      onSuccess: () =>
        queryClient.invalidateQueries({
          queryKey: getGetUserQueryKey(),
        }),
      onError: (error, variables) => {
        if (error) {
          if (variables.params.quickBuyAmountUsd === '0') {
            toast.error(t('errors.quick-buy-zero-validation'));
          } else {
            toast.error(t('errors.quick-buy-set-error'));
          }
        }
      },
    },
  });

  const debouncedSetQuickBuyAmount = useDebounceCallback(setQuickBuyAmount, 1_000);

  const handleQuickBuyAmountChange = (value: string) => {
    if (isPending || !value) {
      return;
    }
    debouncedSetQuickBuyAmount({
      params: { quickBuyAmountUsd: value },
    });
  };

  const handleResultDialogClose = () => {
    setBuyTxHashes([]);
    setBuyToken(undefined);
  };

  const handleQuickBuyToken = (address: string, chain: Chain) => {
    if (isPending) {
      return;
    }

    setBuyToken({ address, chain });
    quickBuyToken(
      { tokenAddress: address, data: { chain } },
      {
        onSuccess: (result) => {
          setBuyTxHashes([result.txHash]);
        },
      },
    );
  };

  return (
    <Card className="flex max-w-(--breakpoint-lg) flex-col gap-y-2 lg:mx-auto" variant="area">
      <DetailListHeader
        className="gap-2"
        subtitle={t('hot-tokens-right-now-subtitle')}
        title={t('hot-tokens-right-now')}
      >
        <div className="flex flex-col items-center gap-1 sm:flex-row">
          <QuickBuyInput quickBuyAmountUsd={user?.quickBuyAmountUsd} onChange={handleQuickBuyAmountChange} />
        </div>
      </DetailListHeader>

      <DetailListContainer>
        <HotTokensVirtualList
          isPublic={false}
          quickBuyAmountUsd={user?.quickBuyAmountUsd}
          onQuickBuy={handleQuickBuyToken}
        />
      </DetailListContainer>

      {buyToken ? (
        <ResultDialog
          isSubmitting={isPending}
          submitError={error ? t('errors.quick-buy-failed') : ''}
          tokenAddress={buyToken.address}
          tokenChain={buyToken.chain}
          txHashes={buyTxHashes}
          onClose={handleResultDialogClose}
        />
      ) : null}
    </Card>
  );
};
