'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { MAX_WALLET_COUNT_PER_CHAIN } from '@/constants/config';
import { ROUTES } from '@/constants/routes';
import { Chain, getGetAllUserWalletsV3QueryKey, useCreateNewUserWalletV2, usePatchUserWallet } from '@/lib/api';
import { useRedirectUrl } from '@/module/routing/use-redirect-url';
import { useWallets } from '@/module/wallet-detail/hooks/use-wallets';
import { MAX_WALLET_NAME_LENGTH, MIN_WALLET_NAME_LENGTH } from '@/module/wallets/constants';
import { SELECTED_TAB_QUERY } from '@/module/wallets/use-tabs-literal';

const useSchema = (walletCounts: Record<string, number>, enabledChains: Chain[]) => {
  const t = useTranslations('setup-wallet.validations');

  return z
    .object({
      name: z
        .string()
        .min(MIN_WALLET_NAME_LENGTH, {
          message: t('min-wallet-name-length', {
            min: MIN_WALLET_NAME_LENGTH,
          }),
        })
        .max(MAX_WALLET_NAME_LENGTH, {
          message: t('max-wallet-name-length', { max: MAX_WALLET_NAME_LENGTH }),
        }),
      chain: z.enum(enabledChains as [Chain, ...Chain[]], {
        message: t('choose-a-chain'),
      }),
    })
    .refine((data) => (walletCounts[data.chain] ?? 0) < MAX_WALLET_COUNT_PER_CHAIN, {
      message: t('max-wallet-limit-hit'),
      path: ['chain'],
    });
};

type CreateWalletFormSchema = z.infer<ReturnType<typeof useSchema>>;

export const useCreateWalletForm = () => {
  const selectedChain = useSearchParams().get(SELECTED_TAB_QUERY) ?? '';
  const [walletAddress] = useState('');
  const { walletCounts, enabledChains } = useWallets();
  const schema = useSchema(walletCounts, enabledChains);

  const redirectUrl = useRedirectUrl();

  const createWallet = useCreateNewUserWalletV2();
  const patchWallet = usePatchUserWallet();

  const queryClient = useQueryClient();

  const form = useForm<CreateWalletFormSchema>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      chain: selectedChain ? Chain[selectedChain as Chain] : enabledChains[0],
    },
  });

  const onSubmit = async (data: CreateWalletFormSchema) => {
    try {
      const wallet = await createWallet.mutateAsync(
        { data: { chain: data.chain } },
        {
          onSuccess: async () => {
            await queryClient.invalidateQueries({
              queryKey: getGetAllUserWalletsV3QueryKey({
                useSelectedChains: true,
              }),
            });
          },
        },
      );

      await patchWallet.mutateAsync({
        walletId: wallet.walletId,
        data: { customName: data.name },
      });

      await queryClient.invalidateQueries({
        queryKey: getGetAllUserWalletsV3QueryKey({ useSelectedChains: true }),
      });

      const chain = form.getValues('chain');

      window.location.href = ROUTES.SETUP_WALLET.DONE('create', wallet.walletAddress, chain, redirectUrl);
    } catch (error) {
      let errorMessage = 'An unknown error occurred';
      if (error instanceof AxiosError) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access -- FIXME: Fix Error types
        errorMessage = error.response?.data.message;
      } else if (error instanceof ReferenceError) {
        errorMessage = error.message;
      }

      form.setError('root.serverError', {
        message: errorMessage,
      });
    }
  };

  return { form, onSubmit, walletAddress };
};
