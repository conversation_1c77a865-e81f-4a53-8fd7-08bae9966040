'use client';

import { useTranslations } from 'next-intl';
import React, { useMemo } from 'react';

import { STALE_TIME_PORTFOLIO } from '@/api/constants';
import { DONUT_COLORS } from '@/components/charts/donut-chart/constants';
import { DonutChart } from '@/components/charts/donut-chart/donut-chart';
import { DetailListHeader } from '@/components/data-list/detail-list-header';
import { ProfitCard } from '@/components/profit-card';
import { Card } from '@/components/ui/card';
import { useGetPortfolioOverview } from '@/lib/api';
import { formatPercentage } from '@/lib/formatters/format-percentage';
import { numericallyAsc, numericallyDesc } from '@/lib/sort/sort-numerically';
import { AllocationTitle } from '@/module/home/<USER>/allocation-title';
import { getChainConfig } from '@/module/multichain/get-chain-config';

export const ManualTradedContent: React.FC = () => {
  const t = useTranslations('asset-allocation');

  const { data } = useGetPortfolioOverview({
    query: { staleTime: STALE_TIME_PORTFOLIO },
  });

  const manualTradedData = useMemo(
    () => data?.pieChartValues.filter((item) => item.type === 'MANUAL'),
    [data?.pieChartValues],
  );

  const total = useMemo(
    () => manualTradedData?.reduce((accumulator, item) => accumulator + Number(item.portfolioValueUsd), 0),
    [manualTradedData],
  );

  const chartData = useMemo(
    () =>
      manualTradedData
        ?.toSorted((a, b) => numericallyAsc(Number(a.portfolioValueUsd), Number(b.portfolioValueUsd)))
        .map((item, index) => {
          const value = total ? Number(item.portfolioValueUsd) / total : 0;
          return {
            value: String(value),
            formattedValue: formatPercentage(value, 0),
            color: DONUT_COLORS[index % DONUT_COLORS.length] ?? '',
            chain: item.chain,
          };
        }),
    [manualTradedData, total],
  );

  return (
    <Card className="flex max-w-(--breakpoint-lg) flex-col gap-y-2 lg:mx-auto" variant="area">
      <DetailListHeader title={t('manual-traded')} />

      <DonutChart data={chartData} />

      <div className="mt-3 flex flex-col flex-wrap gap-2 sm:flex-row">
        {manualTradedData
          ?.toSorted((a, b) => numericallyDesc(Number(a.portfolioValueUsd), Number(b.portfolioValueUsd)))
          .map((item) => (
            <ProfitCard
              key={item.chain}
              className="flex-1 sm:min-w-[1/3]"
              title={
                <AllocationTitle
                  dotColor={
                    // map dot color to the chart data
                    chartData?.find((chartItem) => chartItem.chain === item.chain)?.color
                  }
                  title={t('on-chain', {
                    chain: getChainConfig(item.chain).chainName,
                  })}
                />
              }
              trend={item.portfolioPnlFraction}
              value={item.portfolioValueUsd}
            />
          ))}
      </div>
    </Card>
  );
};
