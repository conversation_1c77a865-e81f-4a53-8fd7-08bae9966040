import { useEffect, useState } from 'react';

import type { useGetTokenDetailAsUserV2 } from '@/lib/api';

export const useTradeStatusFetchCount = (query: ReturnType<typeof useGetTokenDetailAsUserV2>) => {
  const [fetchCount, setFetchCount] = useState(0);

  useEffect(() => {
    if (query.isRefetching) {
      // Increment the count when a new fetch starts
      setFetchCount((previous) => previous + 1);
    }
  }, [query.isRefetching]);

  return fetchCount;
};
