import type { Chain } from '@/lib/api';
import { getChainConfig } from '@/module/multichain/get-chain-config';
import { PresetButton } from '@/module/token-detail/manual-trading/preset-button';

interface Props {
  chain: Chain;
  onSelect: (value: string) => void;
}

export const NativePresets = ({ onSelect, chain }: Props) => {
  const chainConfig = getChainConfig(chain);

  return (
    <div className="flex flex-wrap gap-1">
      {chainConfig.buyPresets.map((preset, index) => (
        <PresetButton
          key={index}
          className={preset.className}
          label={
            <>
              <span data-testid={`chain-icon-${chain}`}>{chainConfig.tokenImageBw({ className: 'size-2 h-2' })}</span>
              {preset.value}
            </>
          }
          shadowVariant="flat"
          onClick={() => {
            onSelect(preset.value);
          }}
        />
      ))}
    </div>
  );
};
