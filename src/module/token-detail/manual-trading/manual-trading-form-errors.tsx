import BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';
import type { ReactNode} from 'react';
import { useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';

import { AlertMessage, AlertMessageContainer } from '@/components/alert-message';
import { formatNumber } from '@/lib/formatters/format-number';

interface Props {
  maxBuyValue: string;
  hasNoBalance: boolean;
  onFormError?: (hasError: boolean) => void;
  fallback?: ReactNode;
}

export const ManualTradingFormErrors: React.FC<Props> = ({ maxBuyValue, hasNoBalance, onFormError, fallback }) => {
  const form = useFormContext();
  const { isSell, ethAmount } = form.getValues();
  const formErrors = form.formState.errors;
  const t = useTranslations('token-detail');
  const hasFormErrors = Object.keys(formErrors).length > 0;

  const isMaxBuyExceeded = useMemo(() => {
    const maxBuyValueBn = new BigNumber(maxBuyValue);
    if (isSell || maxBuyValueBn.isZero()) {
      return false;
    }

    return new BigNumber(((ethAmount as string) || undefined) ?? '').gt(maxBuyValue);
  }, [ethAmount, maxBuyValue, isSell]);

  useEffect(() => {
    onFormError?.(hasNoBalance || hasFormErrors || isMaxBuyExceeded);
  }, [hasFormErrors, hasNoBalance, isMaxBuyExceeded, onFormError]);

  if (hasNoBalance) {
    return (
      <AlertMessageContainer variant="error">
        <AlertMessage type="container" variant="error">
          {t('trading.insufficient-balance')}
        </AlertMessage>
      </AlertMessageContainer>
    );
  }

  if (hasFormErrors) {
    return (
      <AlertMessageContainer variant="error">
        <AlertMessage type="container" variant="error">
          {/* eslint-disable-next-line @typescript-eslint/no-base-to-string -- TODO: Parse objects properly globally*/}
          {Object.entries(formErrors).map(([key, error]) => error && <div key={key}>{String(error.message)}</div>)}
        </AlertMessage>
      </AlertMessageContainer>
    );
  }

  if (isMaxBuyExceeded) {
    return (
      <AlertMessageContainer variant="warning">
        <AlertMessage type="container" variant="warning">
          {t('trading.max-buy-error-message', {
            amount: formatNumber(maxBuyValue),
          })}
        </AlertMessage>
      </AlertMessageContainer>
    );
  }

  return fallback;
};
