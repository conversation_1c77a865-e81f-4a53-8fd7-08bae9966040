'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { useDebounceValue } from 'usehooks-ts';
import { z } from 'zod';

import { REFETCH_INTERVAL_15_SECONDS } from '@/api/constants';
import {
  type Chain,
  type GetDexResult,
  getGetTokenDetailAsUserV2QueryOptions,
  useGetPredictedEthAmountOnSellV2,
  useGetPredictedTokenAmountOnBuyV2,
} from '@/lib/api';
import { isLeft } from '@/lib/either';
import { getChainConfig } from '@/module/multichain/get-chain-config';
import { buyToken } from '@/module/token-detail/actions/buy-token';
import { sellToken } from '@/module/token-detail/actions/sell-token';
import { getDefaultOrderTab, zOrderType } from '@/module/token-detail/manual-trading/entities/order-type';
import { makeTokenPosition } from '@/module/token-detail/manual-trading/use-token-balance';

const DEFAULT_DEBOUCE_INTERVAL_MS = 200;

const useSchema = (tokenAddress: string, chain: Chain) => {
  const t = useTranslations('token-detail.trading');
  const queryClient = useQueryClient();

  return z
    .object({
      isSell: z.boolean(),
      orderType: zOrderType,
      walletId: z.string().min(1, t('wallet-required')),
      nativeAmount: z.string(),
      tokenAmount: z.string(),
    })
    .refine((formData) => (formData.isSell ? new BigNumber(formData.tokenAmount).gt(0) : true), {
      path: ['tokenAmount'],
      message: t('token-required'),
    })
    .refine(
      async (formData) => {
        if (!formData.isSell) {
          return true;
        }

        if (formData.walletId) {
          const tokenInfo = await queryClient.fetchQuery(getGetTokenDetailAsUserV2QueryOptions(chain, tokenAddress));

          return makeTokenPosition(tokenInfo, formData.walletId).gte(formData.tokenAmount);
        }

        return Promise.resolve(false);
      },
      {
        path: ['tokenAmount'],
        message: t('insufficient-balance'),
      },
    )
    .refine((formData) => (formData.isSell ? true : new BigNumber(formData.nativeAmount).gt(0)), {
      path: ['nativeAmount'],
      message: t('chain-amount-required', {
        chain: getChainConfig(chain).symbol,
      }),
    });
};

export type ManualTradingFormSchema = z.infer<ReturnType<typeof useSchema>>;

interface Props {
  chain: Chain;
  tokenAddress: string;
  dexPairInfo: GetDexResult;
  defaultIsSell: boolean;
  defaultWalletId: string;
}

export const useManualTradingForm = ({ chain, dexPairInfo, tokenAddress, defaultIsSell, defaultWalletId }: Props) => {
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>();
  const [txHashes, setTxHashes] = useState<string[]>();

  const schema = useSchema(tokenAddress, chain);

  const form = useForm<ManualTradingFormSchema>({
    resolver: zodResolver(schema),
    defaultValues: {
      isSell: defaultIsSell,
      walletId: defaultWalletId,
      orderType: getDefaultOrderTab(),
      nativeAmount: '',
      tokenAmount: '',
    },
  });

  const [tokenAmount, nativeAmount, isSell] = form.watch(['tokenAmount', 'nativeAmount', 'isSell']);

  const [debouncedNativeAmount] = useDebounceValue(nativeAmount, DEFAULT_DEBOUCE_INTERVAL_MS);
  const [debouncedTokenAmount] = useDebounceValue(tokenAmount, DEFAULT_DEBOUCE_INTERVAL_MS);

  const debouncedEthAmountGtZero = new BigNumber(debouncedNativeAmount).gt(0);
  const debouncedTokenAmountGtZero = new BigNumber(debouncedTokenAmount).gt(0);

  const predictedTokenAmountToBuy = useGetPredictedTokenAmountOnBuyV2(
    tokenAddress,
    {
      buyForCurrencyNativeAmount: debouncedNativeAmount,
      dexPairInfo,
      chain,
    },
    {
      query: {
        enabled: debouncedEthAmountGtZero && !isSell,
        refetchInterval: REFETCH_INTERVAL_15_SECONDS,
        staleTime: REFETCH_INTERVAL_15_SECONDS,
      },
    },
  );

  useEffect(() => {
    if (!predictedTokenAmountToBuy.data || isSell) {
      return;
    }

    form.setValue('tokenAmount', predictedTokenAmountToBuy.data.predictedTokenNativeAmount);
  }, [form, predictedTokenAmountToBuy.data, isSell]);

  const predictedNativeAmountToSell = useGetPredictedEthAmountOnSellV2(
    tokenAddress,
    {
      toSellTokenNativeAmount: debouncedTokenAmount,
      dexPairInfo,
      chain,
    },
    {
      query: {
        enabled: debouncedTokenAmountGtZero && isSell,
        refetchInterval: REFETCH_INTERVAL_15_SECONDS,
        staleTime: REFETCH_INTERVAL_15_SECONDS,
      },
    },
  );

  useEffect(() => {
    if (!new BigNumber(nativeAmount).gt(0) && !isSell) {
      form.setValue('tokenAmount', '0');
      form.clearErrors('tokenAmount');
    } else if (!new BigNumber(tokenAmount).gt(0) && isSell) {
      form.setValue('nativeAmount', '0');
      form.clearErrors('nativeAmount');
    }
  }, [isSell, form, nativeAmount, tokenAmount]);

  useEffect(() => {
    if (!predictedNativeAmountToSell.data || !isSell) {
      return;
    }

    form.setValue('nativeAmount', predictedNativeAmountToSell.data.predictedCurrencyNativeAmount);
  }, [form, predictedNativeAmountToSell.data, isSell]);

  const submitTrade: SubmitHandler<ManualTradingFormSchema> = async ({ walletId, nativeAmount, tokenAmount }) => {
    setError('');

    if (isSell) {
      const result = await sellToken({
        walletId,
        tokenAddress,
        tokenAmount,
        dexPairInfo,
      });

      if (isLeft(result)) {
        setError(result.left);
        return;
      }

      // invalidate user txs query so we can update the tx status via WS
      await queryClient.invalidateQueries(getGetTokenDetailAsUserV2QueryOptions(chain, tokenAddress));

      setTxHashes(result.right);
    } else {
      const result = await buyToken({
        walletId,
        tokenAddress,
        nativeAmount,
        dexPairInfo,
      });

      if (isLeft(result)) {
        setError(result.left);
        return;
      }

      // invalidate user txs query to update the tx status via WS
      await queryClient.invalidateQueries(getGetTokenDetailAsUserV2QueryOptions(chain, tokenAddress));

      setTxHashes([result.right]);
    }
  };

  return {
    form,
    submitTrade,
    error,
    txHashes,
    isTokenAmountPending: predictedTokenAmountToBuy.isFetching,
    isNativeAmountPending: predictedNativeAmountToSell.isFetching,
  };
};
