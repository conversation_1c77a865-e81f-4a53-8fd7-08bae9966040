import type BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';

import { PresetButton } from '@/module/token-detail/manual-trading/preset-button';

interface Props {
  tokenBalance?: BigNumber;
  isSellingAll?: boolean;
  onSelect: (value: string) => void;
}

export const TokenPresets = ({ isSellingAll, onSelect, tokenBalance }: Props) => {
  const t = useTranslations('token-detail');

  return (
    <div className="flex flex-wrap gap-1">
      <PresetButton
        label="10%"
        shadowVariant="flat"
        onClick={() => {
          onSelect(tokenBalance ? tokenBalance.times('0.1').toString() : '0');
        }}
      />
      <PresetButton
        className="hidden xl:block"
        label="25%"
        shadowVariant="flat"
        onClick={() => {
          onSelect(tokenBalance ? tokenBalance.times('0.25').toString() : '0');
        }}
      />
      <PresetButton
        label="50%"
        shadowVariant="flat"
        onClick={() => {
          onSelect(tokenBalance ? tokenBalance.times('0.5').toString() : '0');
        }}
      />
      <PresetButton
        className="hidden 2xl:block"
        label="75%"
        shadowVariant="flat"
        onClick={() => {
          onSelect(tokenBalance ? tokenBalance.times('0.75').toString() : '0');
        }}
      />
      <PresetButton
        isActive={isSellingAll}
        label={t('preset-all')}
        shadowVariant="flat"
        onClick={() => {
          onSelect(tokenBalance ? tokenBalance.toString() : '0');
        }}
      />
    </div>
  );
};
