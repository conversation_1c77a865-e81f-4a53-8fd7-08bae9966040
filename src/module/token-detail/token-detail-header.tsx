import { isTokenDexResult } from '@/api/typeguards/is-token-dexresult';
import { AppNavigation } from '@/components/app-header/app-navigation';
import { ProfileLink } from '@/components/profile-link';
import { ROUTES } from '@/constants/routes';
import {
  type Chain,
  getGetTokenDetailAsAnonymousUserV2QueryOptions,
} from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { BackLink } from '@/module/routing/back-link';
import { TokenDetailAvatar } from '@/module/token-detail/token-detail-avatar';

interface Props {
  chain: Chain;
  address: string;
  leagueSystemEnabled: boolean;
}

export const TokenDetailHeader = async ({
  chain,
  address,
  leagueSystemEnabled,
}: Props) => {
  const queryClient = getQueryClient();

  const data = await queryClient.fetchQuery(
    getGetTokenDetailAsAnonymousUserV2QueryOptions(chain, address)
  );

  return (
    <div className="grid grid-cols-2 items-center px-0 py-2 sm:px-0 sm:pb-3 sm:pt-4 md:grid-cols-[1fr_auto_1fr]">
      <div className="flex">
        <BackLink defaultUrl={ROUTES.MANUAL_TRADING.ROOT} />
        {isTokenDexResult(data.tokenInfo) ? <TokenDetailAvatar
            chain={chain}
            tokenImageUrl={data.tokenInfo.dexInfo.info?.imageUrl}
            tokenName={data.tokenInfo.dexInfo.baseToken.name}
            tokenSymbol={data.tokenInfo.dexInfo.baseToken.symbol}
          /> : null}
      </div>
      <AppNavigation
        className="hidden sm:flex"
        leagueSystemEnabled={leagueSystemEnabled}
      />
      <div className="flex items-center justify-end gap-3">
        <ProfileLink className="hidden sm:block" />
      </div>
    </div>
  );
};
