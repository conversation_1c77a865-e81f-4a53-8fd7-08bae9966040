/* eslint-disable no-magic-numbers */
import { differenceInDays, differenceInHours, differenceInMinutes, parseISO } from 'date-fns';

import { TimeRange } from '@/lib/api';

// get adaptive time frames based on token age
export const getAvailableTimeframes = (tokenCreatedAt: string): TimeRange[] => {
  const now = new Date();
  const tokenCreationDate = parseISO(tokenCreatedAt);

  const minutesOld = differenceInMinutes(now, tokenCreationDate);
  const hoursOld = differenceInHours(now, tokenCreationDate);
  const daysOld = differenceInDays(now, tokenCreationDate);

  const timeframes: TimeRange[] = [TimeRange.ALL];

  // FIXME: Shouldn't this be >= 67?
  if (minutesOld >= 65) {
    timeframes.unshift(TimeRange.DAY);
  }

  if (hoursOld >= 25) {
    timeframes.unshift(TimeRange.WEEK);
  }

  if (daysOld >= 8) {
    timeframes.unshift(TimeRange.MONTH);
  }

  if (daysOld >= 32) {
    timeframes.unshift(TimeRange.YEAR);
  }

  // tokens up to 30 minutes old
  timeframes.unshift(TimeRange.HOUR);

  return timeframes;
};
