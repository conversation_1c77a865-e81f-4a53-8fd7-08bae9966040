import { Skeleton } from '@/components/skeleton';
import { cn } from '@/lib/utils';

interface Props {
  variant?: 'component' | 'overview';
  className?: string;
}

export const TinyRankCardSkeleton = ({ variant, className }: Props) => (
  <div
    className={cn(
      'flex gap-1 shadow-2xl rounded-2xl py-1 px-1.5',
      variant === 'overview' ? 'bg-surface-primary p-2' : 'bg-surface-brand-1-alpha-cards',
      className,
    )}
  >
    <div className="flex w-full flex-col gap-1 mt-1">
      <div className="flex flew-row gap-1">
        <div className="relative size-6 rounded-xxs bg-surface-brand-1/10 p-0.5 flex items-center justify-center"></div>

        <div className="flex flex-col gap-1 text-md text-text-brand-1 mt-1">
          <Skeleton className="h-2 w-16" />
          <Skeleton className="h-2 w-24" />
        </div>

        <div className="flex ml-auto flex-col gap-1 text-md mt-1 items-end">
          <Skeleton className="h-2 w-5" />
          <Skeleton className="h-2 w-20" />
        </div>
      </div>

      <Skeleton className="h-1 mt-1 w-full" />

      <Skeleton className="h-2 w-3/4" />
    </div>
  </div>
);
