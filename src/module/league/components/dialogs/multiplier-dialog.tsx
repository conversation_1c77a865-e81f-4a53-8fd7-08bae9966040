import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/ui/dialog';
import Display from '@/components/ui/display';
import { formatRichText } from '@/lib/formatters/format-rich-text';
import { MultiplierBadge } from '@/module/league/components/multiplier-badge';
import { MultiplierBox } from '@/module/league/components/multiplier-box';

interface Props {
  open: boolean;
  onOpenChange?: (open: boolean) => void;
  multiplier: number;
}

export const MultiplierDialog = ({ open, onOpenChange, multiplier }: Props) => {
  const t = useTranslations('fatty-league');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex flex-col sm:gap-y-5 sm:bg-surface-background sm:max-w-[450px] sm:p-5">
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />

        <Display className="text-center" size="M" weight="bold">
          {t.rich('multiplier-dialog.title', formatRichText)}
        </Display>

        <MultiplierBadge
          className="text-2xl px-1.5"
          dotClassName="mr-0.5"
          multiplier={multiplier}
          withDot={true}
        />

        <p className="text-md font-semibold text-text-secondary text-center sm:px-2">
          {t('multiplier-dialog.text')}
        </p>

        <div className="flex flex-col gap-y-2">
          <MultiplierBox
            className="p-1 pr-2 rounded-xs gap-x-2"
            description={t('multipliers.fatty-league.text')}
            icon={'🏆'}
            name={t('multipliers.fatty-league.name')}
            value={multiplier}
          />

          <MultiplierBox
            className="p-1 pr-2 rounded-xs gap-x-2"
            description={t('multipliers.streak.text')}
            icon={'🔥'}
            name={t('multipliers.streak.name')}
            value={multiplier}
          />
        </div>

        <Button
          className="w-full mb-5 sm:mb-0 mt-auto sm:mt-0"
          variant="primary"
        >
          {t('multiplier-dialog.continue')}
        </Button>
      </DialogContent>
    </Dialog>
  );
};
