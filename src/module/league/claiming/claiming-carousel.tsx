import { keepPreviousData, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useCallback, useMemo, useState } from 'react';

import { toast } from '@/components/toast';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from '@/i18n/routing';
import type { SearchUserFattyCardsResult } from '@/lib/api';
import {
  getSearchUserFattyCardsQueryOptions,
  useClaimUserFattyCard,
  useDisplayUserFattyCards,
  useSearchUserFattyCards,
} from '@/lib/api';
import { cn } from '@/lib/utils';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/module/league/components/carousel';
import { HiddenRewardCardSkeleton } from '@/module/league/rewards/hiden-reward-card-skeleton';

import type { Rarity } from '../rewards/reward-config';

import { ClaimingCarouselCard } from './claiming-carousel-card';

const SKELETON_CARDS_COUNT = 3;
const CAROUSE_PAGE_LIMIT_CARDS_COUNT = 3;

export const ClaimingCarousel = () => {
  const t = useTranslations('rewards');
  const router = useRouter();

  const [activeIndex, setActiveIndex] = useState<number>(0);

  const { data: fattyCards, isLoading } = useSearchUserFattyCards(
    {
      claimed: false,
    },
    {
      query: {
        placeholderData: keepPreviousData,
      },
    },
  );

  const { data: displayedFattyCards } = useSearchUserFattyCards({
    displayed: true,
  });

  const queryClient = useQueryClient();

  const { mutate, isPending } = useDisplayUserFattyCards({
    mutation: {
      onMutate: (data) => {
        const addedCard = fattyCards?.find((card) => card.userFattyCardId === data.data.userFattyCardIds[0]);

        queryClient.setQueryData(getSearchUserFattyCardsQueryOptions({ displayed: true }).queryKey, (previousCards) => {
          if (previousCards) {
            return [...previousCards, addedCard] as SearchUserFattyCardsResult[];
          }

          return [addedCard as SearchUserFattyCardsResult];
        });
      },
    },
  });

  const { mutateAsync: claimUserFattyCard } = useClaimUserFattyCard();

  const currentCard = fattyCards?.[activeIndex];

  const currentDisplayedCardIds = useMemo(
    () => (displayedFattyCards ?? []).map(({ userFattyCardId }) => userFattyCardId),
    [displayedFattyCards],
  );

  const onCardReveal = useCallback(
    (userCardId: string) => {
      mutate({
        data: {
          userFattyCardIds: [userCardId],
        },
      });
    },
    [mutate],
  );

  const onCardClaim = useCallback(
    async (cardId: string) => {
      await claimUserFattyCard(
        { userFattyCardId: cardId },
        {
          onSuccess: async () => {
            toast.success(t('reward-claimed'));
            await queryClient.invalidateQueries(getSearchUserFattyCardsQueryOptions({ claimed: false }));
            // if last card is claimed redirec to league
            if (fattyCards?.length === 1) {
              router.push(ROUTES.LEAGUE.ROOT);
            }
          },
        },
      );
    },
    [claimUserFattyCard, queryClient, t, router, fattyCards?.length],
  );

  const cardsCount = useMemo(() => fattyCards?.length ?? 0, [fattyCards]);

  return (
    <Carousel
      className="w-full max-w-[1024px]  mx-auto my-3"
      opts={{
        align: 'center',
        active: !isLoading,
        loop: true,
        slidesToScroll: 1,
      }}
    >
      <CarouselContent className="w-full ml-0 min-h-[720px] py-6">
        {isLoading
          ? [...Array<number>(SKELETON_CARDS_COUNT)].map((_, index) => (
              <CarouselItem key={index} className="md:basis-1/3" index={index - 1}>
                <HiddenRewardCardSkeleton variant="large" />
              </CarouselItem>
            ))
          : null}

        {fattyCards
          ? fattyCards.map((fattyCard, index) => (
              <CarouselItem
                key={fattyCard.userFattyCardId}
                className={cn('md:basis-1/3 px-3 h-full', {
                  'mx-auto': fattyCards.length === 1,
                })}
                index={cardsCount <= CAROUSE_PAGE_LIMIT_CARDS_COUNT ? index - 1 : index}
                onSlide={setActiveIndex}
              >
                <ClaimingCarouselCard
                  claimableReward={fattyCard.donutReward}
                  id={fattyCard.userFattyCardId}
                  isDisplayed={currentDisplayedCardIds.includes(fattyCard.userFattyCardId)}
                  rarity={fattyCard.rarity as Rarity}
                  onClaim={onCardClaim}
                  onFlip={onCardReveal}
                />
              </CarouselItem>
            ))
          : null}
      </CarouselContent>
      <div className="flex mt-3 items-center justify-center gap-1 lg:gap-3">
        <CarouselPrevious className={cn(isPending && 'opacity-10 !cursor-not-allowed')} />
        <Button
          className={cn('px-2 lg:px-6', {
            'opacity-10 !cursor-not-allowed':
              currentDisplayedCardIds.includes(currentCard?.userFattyCardId ?? '') || isPending,
          })}
          variant="primary"
          onClick={() => {
            onCardReveal(currentCard?.userFattyCardId ?? '');
          }}
        >
          {t('reveal-prize')}
        </Button>
        <CarouselNext className={cn(isPending && 'opacity-10 !cursor-not-allowed')} />
      </div>
    </Carousel>
  );
};
