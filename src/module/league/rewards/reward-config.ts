export enum Rarity {
  COMMON = 'Common',
  UNCOMMON = 'Uncommon',
  RARE = 'Rare',
  LEGENDARY = 'Legendary',
  MYTHICAL = 'Mythical',
  JACKPOT = 'Jackpot',
}

type TranslationKey = 'common' | 'jackpot' | 'legendary' | 'major' | 'mythical' | 'rare' | 'uncommon';
type BadgeVariant = 'success-content' | 'success-subtle' | 'success';

export interface RewardConfigEntry {
  label: string;
  rarity: Rarity;
  translationKey: TranslationKey;
  bgVariant: string;
  badgeVariant: BadgeVariant;
  downloadIconColor: string;
  donutAmountColor: string;
  textClasses: {
    caption: string;
    text: string;
  };
}

export const rewardConfig: Record<Rarity, RewardConfigEntry> = {
  [Rarity.COMMON]: {
    rarity: Rarity.COMMON,
    label: 'common',
    translationKey: 'common',
    bgVariant: 'bg-surface-primary',
    badgeVariant: 'success',
    downloadIconColor: 'text-border-secondary',
    donutAmountColor: 'text-white',
    textClasses: {
      caption: 'text-text-brand-1',
      text: 'text-text-secondary',
    },
  },
  [Rarity.UNCOMMON]: {
    rarity: Rarity.UNCOMMON,
    label: 'uncommon',
    translationKey: 'uncommon',
    bgVariant: 'bg-surface-primary',
    badgeVariant: 'success',
    downloadIconColor: 'text-border-secondary',
    donutAmountColor: 'text-white',
    textClasses: {
      caption: 'text-text-brand-1',
      text: 'text-text-secondary',
    },
  },
  [Rarity.RARE]: {
    rarity: Rarity.RARE,
    label: 'rare',
    translationKey: 'rare',
    bgVariant: 'bg-surface-primary',
    badgeVariant: 'success',
    downloadIconColor: 'text-border-secondary',
    donutAmountColor: 'text-white',
    textClasses: {
      caption: 'text-text-brand-1',
      text: 'text-text-secondary',
    },
  },
  [Rarity.LEGENDARY]: {
    rarity: Rarity.LEGENDARY,
    label: 'legendary',
    translationKey: 'legendary',
    bgVariant: 'bg-surface-primary',
    badgeVariant: 'success',
    donutAmountColor: 'text-white',
    downloadIconColor: 'text-border-secondary',
    textClasses: {
      caption: 'text-text-brand-1',
      text: 'text-text-secondary',
    },
  },
  [Rarity.MYTHICAL]: {
    rarity: Rarity.MYTHICAL,
    label: 'mythical',
    translationKey: 'mythical',
    bgVariant: 'bg-surface-brand-2',
    badgeVariant: 'success-content',
    downloadIconColor: 'text-white',
    donutAmountColor: 'text-white',
    textClasses: {
      caption: 'text-white',
      text: 'text-white/60',
    },
  },
  [Rarity.JACKPOT]: {
    rarity: Rarity.JACKPOT,
    label: 'jackpot',
    translationKey: 'jackpot',
    bgVariant: 'bg-surface-brand-1',
    badgeVariant: 'success-subtle',
    downloadIconColor: 'text-black',
    donutAmountColor: 'text-black',
    textClasses: {
      caption: 'text-black',
      text: 'text-text-secondary',
    },
  },
};
