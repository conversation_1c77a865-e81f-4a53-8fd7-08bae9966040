'use client';

import { useSpring, animated, easings } from '@react-spring/web';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useRef } from 'react';

import { Download01 } from '@/assets';
import FattyHappy from '@/assets/images/happy-fatty-shadow.webp';
import Confetti from '@/assets/league/rewards/confetti.webp';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Display from '@/components/ui/display';
import { exportHtmlElement } from '@/lib/dom/export-html-element';
import { formatInteger } from '@/lib/formatters/format-integer';
import { formatNumber } from '@/lib/formatters/format-number';
import { cn } from '@/lib/utils';

import { Rarity, rewardConfig } from './reward-config';

interface Props {
  cardId: string;
  rarity: Rarity;
  className?: string;
  onClaim: (id: string) => void;
  claimableReward: number;
}

const FORMAT_OFFSET = -3;
const FIREWORKS_DURATION = 500;
const DOWNLOAD_BUTTON_ID = 'download-button';

const filter = (node: Node) => {
  if (node instanceof HTMLElement) {
    return ![DOWNLOAD_BUTTON_ID].includes(node.id);
  }

  return true;
};

export const DisplayedRewardCard = ({ cardId, rarity, className, onClaim, claimableReward }: Props) => {
  const { translationKey, bgVariant, donutAmountColor, textClasses, badgeVariant, downloadIconColor } =
    rewardConfig[rarity];
  const t = useTranslations(`reward-variants.${translationKey}`);
  const tCommon = useTranslations('reward-variants');
  const cardRef = useRef<HTMLDivElement>(null);

  const handleDownload = async () => {
    if (!cardRef.current) return;

    await exportHtmlElement(cardRef.current, 'fatty-card', 'jpeg', {
      filter,
      style: {
        scale: '0.95',
        borderRadius: '40px',
      },
    });
  };

  const [springs, api] = useSpring(() => ({
    from: { x: 0, opacity: 1, rotate: 0, scale: 1 },
  }));

  const handleClaim = () => {
    // scale up
    void api.start({
      from: { x: 0, opacity: 1, scale: 1 },
      to: { x: 0, opacity: 1, scale: 1.05 },
      config: { duration: 300, easing: easings.easeInOutCubic },
    });

    // rotate and move out
    void api.start({
      from: { x: 0, opacity: 1, scale: 1 },
      to: { x: -1000, opacity: 0, rotate: -90 },
      config: { duration: FIREWORKS_DURATION },
      delay: FIREWORKS_DURATION,
    });

    setTimeout(() => {
      onClaim(cardId);
    }, 2 * FIREWORKS_DURATION);
  };

  return (
    <animated.div
      ref={cardRef}
      className={cn('relative px-2 pt-4 pb-3 rounded-xxl min-w-[300px] gap-y-4 flex flex-col', bgVariant)}
      style={springs}
    >
      <button
        className={cn('absolute right-3 top-3 z-30', downloadIconColor)}
        id={DOWNLOAD_BUTTON_ID}
        type="button"
        onClick={handleDownload}
      >
        <Download01 />
      </button>

      <div className={cn('flex flex-col relative gap-4', className)}>
        <Image alt="Confetti" className="w-full absolute" height={167} src={Confetti} width={371.6} />
        <Image alt={tCommon('happy-fatty-alt')} className="mx-auto" src={FattyHappy} width={162} />
        <div className="flex flex-col items-center w-full gap-y-3">
          <div className="flex flex-col items-center">
            <Badge className="normal-case text-xxs p-1" variant={badgeVariant}>
              {t('badge')}
            </Badge>
            <Display size="L" weight="bold">
              <span className={donutAmountColor}> 🍩 {formatInteger(claimableReward)}</span>
            </Display>
            <Display className={cn('text-text-brand-1', textClasses.caption)} size="M" weight="bold">
              {t('caption')}
            </Display>
          </div>
          <span className={cn('text-md font-semibold text-center', textClasses.text)}>{t('text')}</span>
        </div>
      </div>

      <Button className="w-full mt-1" variant={rarity === Rarity.JACKPOT ? 'invert' : 'primary'} onClick={handleClaim}>
        {tCommon('claim')} {formatNumber(claimableReward).slice(0, FORMAT_OFFSET)}
      </Button>
    </animated.div>
  );
};
