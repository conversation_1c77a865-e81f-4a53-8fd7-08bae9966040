'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { Info } from '@/assets';
import { Card } from '@/components/ui/card';
import Display from '@/components/ui/display';
import { ROUTES } from '@/constants/routes';
import { useGetUserLeaderboardInfo, useSearchUserLeaderboardsInfinite } from '@/lib/api';
import { BotCharacter, BOTS_CONFIG } from '@/module/bot-trading/bots-config';
import { RankVirtualList } from '@/module/league/components/rank-virtual-list';
import { RemainingTime } from '@/module/league/components/remaining-time';
import { TinyRankCard } from '@/module/league/components/tiny-rank-card';
import { TopThreeRanks } from '@/module/league/components/top-three-ranks';

import { LeagueLeaderboardSkeleton } from './league-leaderboard-skeleton';

export const LeagueLeaderboardContent = () => {
  const t = useTranslations('fatty-league.league-leaderboard');

  const { data: userLeaderboardInfo, isLoading: isLoadingUserLeaderboardInfo } = useGetUserLeaderboardInfo();
  const { data: leaderboardTop3, isLoading: isLoadingLeaderboardTop3 } = useSearchUserLeaderboardsInfinite(
    {
      size: 3,
    },
    {
      query: {
        getNextPageParam: () => undefined,
      },
    },
  );

  const top3Ranks = leaderboardTop3?.pages[0]?.content ?? [];

  const isLoading = isLoadingUserLeaderboardInfo || isLoadingLeaderboardTop3;

  if (isLoading) {
    return <LeagueLeaderboardSkeleton />;
  }

  return (
    <div className="md:max-w-7xl w-full mx-auto grid grid-cols-1 lg:grid-cols-2 gap-x-10 gap-y-5 md:gap-y-0">
      <div className="hidden lg:flex h-full mt-10">
        <TopThreeRanks showFireworks={false} top3Ranks={top3Ranks} />
      </div>
      <Card className="relative gap-2 md:flex" variant="area">
        <div className="flex lg:hidden flex-1 justify-end">
          <Link href={ROUTES.LEAGUE.LEADERBOARD_DETAIL(ROUTES.LEAGUE.LEADERBOARD(ROUTES.LEAGUE.ROOT))}>
            <RemainingTime />
          </Link>
        </div>

        <div className="flex gap-x-1">
          <div className="flex flex-col gap-y-1 max-w-[350px]">
            <span className="uppercase text-xs text-text-secondary font-bold tracking-widest">{t('header')}</span>
            <Display size="L" weight="bold">
              {t('title')}
            </Display>

            <p className="text-md text-text-secondary font-semibold">{t('text')}</p>
          </div>

          <div className="hidden lg:flex flex-1 justify-end">
            <Link href={ROUTES.LEAGUE.LEADERBOARD_DETAIL(ROUTES.LEAGUE.LEADERBOARD(ROUTES.LEAGUE.ROOT))}>
              <RemainingTime />
            </Link>
          </div>
        </div>

        <TinyRankCard
          avatarFileId={BOTS_CONFIG[BotCharacter.FatMask].avatarFileId}
          badgeEndAdornment={<Info className="size-1.5" />}
          className="rounded-xs"
          donutCount={parseFloat(userLeaderboardInfo?.donutsGained ?? '0')}
          multiplier={parseFloat(userLeaderboardInfo?.multiplier ?? '0')}
          progressProps={{
            glossyEffectClassName: 'top-px left-[6px] h-[2px]',
            className: 'h-1',
          }}
          rank={userLeaderboardInfo?.rank ?? 0}
          requiredTrades={parseFloat(userLeaderboardInfo?.volumeNeededForNextThreshold ?? '23')}
          variant="component"
          volume={parseFloat(userLeaderboardInfo?.volume ?? '0')}
        />

        <div className="flex lg:hidden h-full items-center">
          <TopThreeRanks showFireworks={false} top3Ranks={top3Ranks} />
        </div>

        <div className="flex flex-col gap-2 mt-3">
          <RankVirtualList />
        </div>
      </Card>
    </div>
  );
};
