import { useTranslations } from 'next-intl';

import { ErrorMessage } from '@/components/error-message';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { useGetUserStreakGeneralInfo } from '@/lib/api'; // adjust import if needed
import { TokenAvatar } from '@/module/manual-trading/components/token-avatar';

import { StreakRewardsSkeleton } from './streak-rewards-skeleton';
import { sortByMultiplierDesc, streakRewardsImageMap } from './utils';

export const StreakRewards = () => {
  const t = useTranslations();
  const { data, isLoading, error } = useGetUserStreakGeneralInfo();

  if (isLoading) {
    return <StreakRewardsSkeleton />;
  }

  if (error) {
    return <ErrorMessage className="text-left text-event-error-content">{t('common.error-loading-data')}</ErrorMessage>;
  }

  return (
    <div className="flex flex-col gap-2">
      {sortByMultiplierDesc(data ?? []).map((streakInfo) => (
        <Card
          key={streakInfo.daysInStreak}
          className="flex flex-row p-1 pr-2 border-none justify-between rounded-xs shadow-primary items-center"
          variant="primary"
        >
          <div className="flex flex-row items-center gap-2">
            <TokenAvatar
              className="my-0 p-[10px]"
              imageUrl={streakRewardsImageMap[streakInfo.multiplier]?.src}
              name={`${streakInfo.daysInStreak} ${t('common.days')}`}
            />
            <p className="text-md font-semibold">
              {streakInfo.daysInStreak} {t('streak.day-streak')}
            </p>
          </div>
          <Badge size="extra-small" variant="alpha-cards">
            {streakInfo.multiplier}x {t('streak.donut-multiplier')}
          </Badge>
        </Card>
      ))}
    </div>
  );
};
