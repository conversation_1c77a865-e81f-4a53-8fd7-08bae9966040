'use client';

import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { TestingTube } from '@/assets/league';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/ui/dialog';
import type { StreakConfig } from '@/module/league/streak/types';

import { DayStreak } from './day-streak';

interface Props {
  multiplier: string;
  streakConfig: StreakConfig[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onClaim?: () => void;
}

export const StreakMultiplierDialog: React.FC<Props> = ({
  multiplier,
  streakConfig,
  open,
  onOpenChange,
  onClaim,
}) => {
  const t = useTranslations('streak');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <VisuallyHidden>
        <DialogTitle>{t('streak-multiplier')}</DialogTitle>
        <DialogDescription>{t('streak-description')}</DialogDescription>
      </VisuallyHidden>
      <DialogContent
        bgVariant="secondary"
        className="h-full p-3 md:p-5 sm:min-w-110 items-center"
        hideClose
      >
        <div className="flex flex-col items-center gap-2">
          <Image alt="Testing Tube" src={TestingTube} />

          <div className="flex flex-col items-center -mt-7">
            <h3 className="text-[128px] font-bold leading-none">
              {multiplier}x
            </h3>
            <h4 className="text-display-m font-bold -mt-0.5">
              {t('donut-multiplier')}
            </h4>
          </div>

          <div className="flex items-center gap-1">
            <div className="text-md font-semibold text-text-secondary text-center">
              {t('active-now')}
            </div>
            <div className="size-0.5 rounded-full bg-surface-subtle" />
            <div className="text-md font-semibold text-text-secondary text-center">
              {t('stays-until-streak-breaks')}
            </div>
          </div>

          <div className="text-md font-semibold text-text-brand-1 mb-1.5">
            {t('streak-reward-unlocked')}
          </div>
        </div>

        <div className="flex flex-col items-center gap-5">
          <DayStreak
            config={streakConfig}
            radioGroupClassName="justify-evenly gap-0"
          />

          <div className="text-md font-semibold text-text-secondary text-center">
            {t('you-earned-x-multiplier', { multiplier })}
          </div>

          <DialogClose asChild>
            <Button className="w-full" onClick={onClaim}>
              {t('claim-donut-multiplier')}
            </Button>
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  );
};
