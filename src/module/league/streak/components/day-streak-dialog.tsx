'use client';

import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import React, { useRef } from 'react';

import { Download01 } from '@/assets';
import { Fire } from '@/assets/league';
import { Button } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog';
import { exportHtmlElement } from '@/lib/dom/export-html-element';
import type { StreakConfig } from '@/module/league/streak/types';

import { DayStreak } from './day-streak';

interface Props {
  daysInStreak: number;
  daysToNextStreak: number;
  nextMultiplier: string;
  streakConfig: StreakConfig[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onContinue?: () => void;
}

export const DayStreakDialog: React.FC<Props> = ({
  daysToNextStreak,
  daysInStreak,
  streakConfig,
  open,
  onOpenChange,
  onContinue,
  nextMultiplier,
}) => {
  const t = useTranslations('streak');
  const dialogRef = useRef<HTMLDivElement>(null);

  const handleDownload = async () => {
    if (!dialogRef.current) return;

    await exportHtmlElement(dialogRef.current, 'streak-dialog', 'png', {
      // to prevent cut off on smaller mobile screens while ) {
      // need to define min height of the dialog
      height: 782,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <VisuallyHidden>
        <DialogTitle>{t('day-streak')}</DialogTitle>
        <DialogDescription>{t('streak-description')}</DialogDescription>
      </VisuallyHidden>
      <DialogContent bgVariant="secondary" className="sm:min-w-110 !p-0 items-center h-full" hideClose>
        <button className="absolute right-3 top-3 z-30" type="button" onClick={handleDownload}>
          <Download01 />
        </button>
        <div ref={dialogRef} className="p-3 md:p-5 items-center  h-full bg-surface-area rounded-lg">
          <div className="flex flex-col items-center gap-0">
            <Image alt="Fire" src={Fire} />

            <div className="text-display-xxxl mt-0.5 font-bold leading-none">{daysInStreak}</div>

            <div className="text-display-m font-bold -mt-0.5 mb-0.5">{t('day-streak')}</div>

            <div className="text-md font-semibold text-text-brand-1 max-w-44 mb-2 md:mb-5 text-center">
              {t('days-left-to-multiplier', {
                count: daysToNextStreak,
                multiplier: nextMultiplier,
              })}
            </div>
          </div>

          <div className="flex flex-col items-center gap-5">
            <DayStreak config={streakConfig} />

            <div className="text-md font-semibold text-text-secondary text-center">{t('streak-description')}</div>

            <DialogClose asChild>
              <Button className="w-full" onClick={onContinue}>
                {t('continue')}
              </Button>
            </DialogClose>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
