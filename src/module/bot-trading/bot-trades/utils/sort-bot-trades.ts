import type { InfiniteData } from '@tanstack/react-query';
import { match } from 'ts-pattern';

import type {
  SearchBotMarketPositionResult,
  searchBotMarketPositions,
  SearchBotMarketPositionsParams,
} from '@/lib/api';

type InfiniteBotTradeData = InfiniteData<
  Awaited<ReturnType<typeof searchBotMarketPositions>>,
  SearchBotMarketPositionsParams['lastId']
>;

const isInfiniteData = (data: InfiniteBotTradeData | SearchBotMarketPositionResult[]): data is InfiniteBotTradeData =>
  'pages' in data;

const sortFunction = (a: SearchBotMarketPositionResult, b: SearchBotMarketPositionResult, isActive = true) => {
  if (isActive) {
    // sort by openTimeStampAt (descending)
    return new Date(b.openTimeStampAt).getTime() - new Date(a.openTimeStampAt).getTime();
  }

  const aTime = a.closedTimeStampAt || a.openTimeStampAt;
  const bTime = b.closedTimeStampAt || b.openTimeStampAt;

  // sort by closedTimeStampAt (descending)
  return new Date(bTime).getTime() - new Date(aTime).getTime();
};

export const sortBotTrades = (data: InfiniteBotTradeData | SearchBotMarketPositionResult[], isActive = true) =>
  match(data)
    .when(isInfiniteData, (infiniteData) =>
      infiniteData.pages.flatMap((page) => page.content).toSorted((a, b) => sortFunction(a, b, isActive)),
    )
    .otherwise((arrayData) => arrayData.toSorted((a, b) => sortFunction(a, b, isActive)));
