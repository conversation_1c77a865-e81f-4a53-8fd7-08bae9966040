'use client';

import { useTranslations } from 'next-intl';
import { parseAsString, useQueryStates } from 'nuqs';
import React from 'react';

import { BOT_DETAIL_LISTS_SIZE } from '@/api/constants';
import { Card } from '@/components/ui/card';
import { useSearchBotMarketPositionsInfinite } from '@/lib/api';
import { sortBotTrades } from '@/module/bot-trading/bot-trades/utils/sort-bot-trades';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { parseTimeRange } from '@/module/bot-trading/utils/timerange-utils';

import { BotTradesList } from './bot-trades-list';

export const BotTradesActive: React.FC = () => {
  const t = useTranslations('bot-trading.bot-trades');

  const [{ timeRange, searchString, botId }] = useQueryStates({
    timeRange: {
      parse: parseTimeRange,
      defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    },
    searchString: parseAsString,
    botId: parseAsString,
  });

  const {
    data = [],
    error,
    isLoading,
    refetch: refetchTrades,
  } = useSearchBotMarketPositionsInfinite(
    botId ?? '',
    {
      timeRange,
      isBotMarketPositionActive: true,
      searchString: searchString ?? undefined,
    },
    { size: BOT_DETAIL_LISTS_SIZE },
    {
      query: {
        enabled: !!botId,
        getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.lastId?.toString() : undefined),
        select: (data) => sortBotTrades(data, true),
      },
    },
  );

  return (
    <Card className="flex w-full flex-col gap-y-3 lg:mx-auto">
      <div className="flex min-h-5 items-center justify-between">
        <span className="text-display-xs font-bold">{t('active')}</span>
      </div>

      <BotTradesList botId={botId} data={data} error={error} isLoading={isLoading} onSellClick={refetchTrades} />
    </Card>
  );
};
