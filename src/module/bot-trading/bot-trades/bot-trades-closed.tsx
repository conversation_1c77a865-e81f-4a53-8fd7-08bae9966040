'use client';

import { useTranslations } from 'next-intl';
import { parseAsString, useQueryStates } from 'nuqs';

import { BOT_DETAIL_LISTS_SIZE } from '@/api/constants';
import { useTimeRangeOptions } from '@/components/charts/area-chart/use-options';
import { Card } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { type TimeRange, useSearchBotMarketPositionsInfinite } from '@/lib/api';
import { sortBotTrades } from '@/module/bot-trading/bot-trades/utils/sort-bot-trades';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { parseTimeRange } from '@/module/bot-trading/utils/timerange-utils';

import { BotTradesList } from './bot-trades-list';

export const BotTradesClosed = () => {
  const t = useTranslations('bot-trading.bot-trades');

  const [{ timeRange, searchString, botId }, setQueryState] = useQueryStates({
    timeRange: {
      parse: parseTimeRange,
      defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    },
    searchString: parseAsString,
    botId: parseAsString,
  });

  const {
    data = [],
    error,
    isLoading,
  } = useSearchBotMarketPositionsInfinite(
    botId ?? '',
    {
      timeRange,
      isBotMarketPositionActive: false,
      searchString: searchString ?? undefined,
    },
    { size: BOT_DETAIL_LISTS_SIZE },
    {
      query: {
        enabled: !!botId,
        getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.lastId?.toString() : undefined),
        select: (data) => sortBotTrades(data, false),
      },
    },
  );

  const timeRangeOptions = useTimeRangeOptions();

  return (
    <Card className="flex w-full flex-col gap-y-3">
      <div className="flex min-h-5 items-center justify-between">
        <span className="text-display-xs font-bold">{t('closed')}</span>
        <Tabs defaultValue={timeRange} onValueChange={(value) => void setQueryState({ timeRange: value as TimeRange })}>
          <TabsList className="flex w-auto border">
            {timeRangeOptions.map(({ key, value }) => (
              <TabsTrigger
                key={key}
                className="min-w-8 px-2 py-0.5 text-md font-medium data-[state=active]:font-bold"
                value={key}
              >
                {value}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
      <BotTradesList botId={botId} data={data} error={error} isLoading={isLoading} />
    </Card>
  );
};
