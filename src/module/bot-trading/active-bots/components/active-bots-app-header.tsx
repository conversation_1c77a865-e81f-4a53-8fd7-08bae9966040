import { ProfileLink } from '@/components/profile-link';
import { ROUTES } from '@/constants/routes';
import { BackLink } from '@/module/routing/back-link';

export const ActiveBotsAppHeader = () => (
    <div className="flex items-center justify-between py-2 sm:pb-3 sm:pt-4">
      {/** TODO! resolve this */}
      <BackLink defaultUrl={ROUTES.BOT_TRADING.ROOT} />

      <div className="flex items-center justify-end gap-3">
        <ProfileLink />
      </div>
    </div>
  );
