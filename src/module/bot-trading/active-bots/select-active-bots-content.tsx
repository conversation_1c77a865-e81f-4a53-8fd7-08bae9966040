'use client';

import { useQ<PERSON>yClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useQueryStates } from 'nuqs';
import { useState } from 'react';

import { InfoCard } from '@/components/info-card';
import { toast } from '@/components/toast';
import { Card } from '@/components/ui/card';
import { LoadingButton } from '@/components/ui/loading-button';
import { ROUTES } from '@/constants/routes';
import { usePathname, useRouter } from '@/i18n/routing';
import { getGetMyBotsQueryOptions, type GetMyBotsResult, TimeRange, useUpdateBotState } from '@/lib/api';
import { BotRow } from '@/module/bot-trading/active-bots/components/bot-row';
import { getDefaultActiveBots, isActiveBot } from '@/module/bot-trading/active-bots/utils';
import { MAX_ACTIVE_BOTS_ALLOWED } from '@/module/bot-trading/bot-wizard/constants';
import { useBotWizardApi } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-api';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { parseTimeRange } from '@/module/bot-trading/utils/timerange-utils';

interface Props {
  myBots: GetMyBotsResult[];
}

export const ActiveBotsContent = ({ myBots }: Props) => {
  const t = useTranslations('bot-trading.select-active-bots');
  const tCommon = useTranslations('common');
  const { botId } = useParams();
  const [{ timeRange }] = useQueryStates({
    timeRange: {
      parse: parseTimeRange,
      defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    },
  });
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const pathname = usePathname();

  const [isLoading, setIsLoading] = useState(false);
  const [closeInfoCard, setCloseInfoCard] = useState(false);
  const bot = myBots.find((bot) => bot.id === botId) ?? null;
  const myActiveBots = myBots.filter(isActiveBot);
  const [activeBots, setActiveBots] = useState(getDefaultActiveBots(bot ? [bot, ...myActiveBots] : myActiveBots));

  const numberOfActiveBots = Object.values(activeBots).filter(Boolean).length;

  const { mutateAsync: updateBotState } = useUpdateBotState();

  const { apiCreateBot } = useBotWizardApi();

  const updateChangedBots = async (idsToUpdate: string[]) => {
    try {
      const idsToDeactivate = idsToUpdate.filter((id) => !activeBots[id]);
      const idsToActivate = idsToUpdate.filter((id) => activeBots[id]);

      // we need to deactivate changed bots at first, otherwise we hit max limit on activation
      if (idsToDeactivate.length) {
        const deactivatePromises = idsToDeactivate.map((id) =>
          updateBotState({ botId: id, params: { active: false } }),
        );
        await Promise.all(deactivatePromises);
      }

      if (idsToActivate.length) {
        const activatePromises = idsToActivate.map((id) => updateBotState({ botId: id, params: { active: true } }));
        await Promise.all(activatePromises);
      }
    } catch {
      toast.error(tCommon('something-went-wrong'));
    }
  };

  const invalidateQueries = async () => {
    // My bots on dashboard fetches my bots based on time range
    await Promise.all([
      queryClient.invalidateQueries(
        getGetMyBotsQueryOptions({
          timeRange: timeRange,
        }),
      ),
      // bot activation switch uses all time range bots query
      queryClient.invalidateQueries(
        getGetMyBotsQueryOptions({
          timeRange: TimeRange.ALL,
        }),
      ),
    ]);
  };

  const onContinue = async () => {
    if (!bot) {
      push(ROUTES.BOT_TRADING.ROOT);
      return;
    }

    // only launched bots can be updated
    const idsToUpdate = myBots.reduce<string[]>((accumulator, bot) => {
      if (!bot.draftCompleteness && bot.id in activeBots && activeBots[bot.id] !== bot.isActive) {
        accumulator.push(bot.id);
      }
      return accumulator;
    }, []);

    const isDraft = bot.draftCompleteness;
    if (isDraft) {
      const isDraftBotActive = activeBots[bot.id];

      // if inactive bot is the draft, we don't need to do anything
      if (!isDraftBotActive) {
        push(ROUTES.BOT_TRADING.ROOT);
        return;
      }

      try {
        setIsLoading(true);
        await updateChangedBots(idsToUpdate);
        const { botId } = await apiCreateBot({ botDraftId: bot.id });
        await invalidateQueries();
        push(ROUTES.BOT_TRADING.LAUNCH(botId, pathname));
        return;
      } catch {
        toast.error(tCommon('something-went-wrong'));
      } finally {
        setIsLoading(false);
      }
    }

    // change isActive state of already launched bots
    if (idsToUpdate.length) {
      try {
        setIsLoading(true);
        await updateChangedBots(idsToUpdate);
        await invalidateQueries();
        push(ROUTES.BOT_TRADING.ROOT);
      } catch {
        toast.error(tCommon('something-went-wrong'));
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="flex items-center justify-center">
      <Card className="mx-auto w-full max-w-2xl space-y-5">
        <div className="flex w-full flex-col gap-2">
          <h2 className="text-display-l font-bold">{t('title')}</h2>
          {myActiveBots.length === MAX_ACTIVE_BOTS_ALLOWED && !closeInfoCard ? (
            <InfoCard
              className="rounded-2xl"
              description={t('caption.description', {
                count: MAX_ACTIVE_BOTS_ALLOWED,
              })}
              title={t('caption.title')}
              variant="warning"
              onClose={() => {
                setCloseInfoCard(true);
              }}
            />
          ) : null}
        </div>
        <div className="flex w-full flex-1 flex-col gap-2">
          {bot ? (
            <BotRow
              bot={bot}
              isActive={activeBots[bot.id] ?? false}
              numOfActiveBots={numberOfActiveBots}
              setActiveBots={setActiveBots}
            />
          ) : null}
          {myActiveBots.map((bot) => (
            <BotRow
              key={bot.id}
              bot={bot}
              isActive={activeBots[bot.id] ?? false}
              numOfActiveBots={numberOfActiveBots}
              setActiveBots={setActiveBots}
            />
          ))}
        </div>
        <LoadingButton className="w-full" disabled={isLoading} isLoading={isLoading} onClick={onContinue}>
          {t('continue')}
        </LoadingButton>
      </Card>
    </div>
  );
};
