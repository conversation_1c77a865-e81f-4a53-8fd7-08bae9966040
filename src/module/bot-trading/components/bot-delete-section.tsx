'use client';

import BigNumber from 'bignumber.js';
import { formatDate } from 'date-fns';
import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import type { FC } from 'react';

import { ROUTES } from '@/constants/routes';
import { useRouter } from '@/i18n/routing';
import { useGetBotById } from '@/lib/api';
import { DATE_TIME_FORMAT } from '@/lib/formatters/format-date';
import { useBotDetailData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-data';
import { BotDeleteDialog } from '@/module/bot-trading/bot-wizard/components/bot-delete-dialog';
import { useBotWizardApi } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-api';

interface Props {
  botId: string;
}

export const BotDeleteSection: FC<Props> = ({ botId }) => {
  const t = useTranslations('bot-trading.create-bot.bot-summary');
  const [botDraftId] = useQueryState('botDraftId');
  const { push } = useRouter();
  const { currentBotDraft } = useBotWizardApi();

  const { botDetail } = useBotDetailData({
    botId,
    enabled: !botDraftId,
  });

  const { data: botData } = useGetBotById(botId, {
    query: {
      enabled: !botDraftId,
    },
  });

  const botBalance = botDetail?.botTotalValueAmountUsd
    ? new BigNumber(botDetail.botTotalValueAmountUsd).toNumber()
    : null;

  const botToDelete = currentBotDraft ?? botData;

  return (
    <div>
      {botToDelete?.createdAt ? (
        <div className="mt-2 flex items-center justify-between">
          <div className="text-md text-text-secondary">
            {t('created-at')} {formatDate(botToDelete.createdAt, DATE_TIME_FORMAT)}
          </div>

          {botToDelete.id ? (
            <BotDeleteDialog
              botBalance={botBalance}
              botId={botToDelete.id}
              isActive={botDetail?.isActive ?? false}
              onDelete={() => {
                push(ROUTES.BOT_TRADING.ROOT);
              }}
            />
          ) : null}
        </div>
      ) : null}
    </div>
  );
};
