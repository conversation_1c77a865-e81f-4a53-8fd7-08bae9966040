import BigNumber from 'bignumber.js';

import { Trend } from '@/components/trend';
import { formatUsd } from '@/lib/formatters/format-usd';
import { cn } from '@/lib/utils';

const POSITIVE_SYMBOL = '+';

interface Props {
  usdValue: string;
  percentage: string;
  fontSize?: 'text-md' | 'text-sm' | 'text-xs';
  badgeSize?: 'big' | 'extra-small' | 'medium';
  className?: string;
}

export const BotTrend: React.FC<Props> = ({ usdValue, percentage, badgeSize, fontSize, className }) => {
  const isPositive = new BigNumber(usdValue).gte(0);

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <span
        className={cn(
          'whitespace-nowrap font-medium text-event-success-content',
          isPositive ? 'text-event-success-content' : 'text-event-error-content',
          fontSize,
        )}
      >
        {isPositive ? POSITIVE_SYMBOL : ''}
        {formatUsd(usdValue)}
      </span>
      <Trend badgeProps={{ size: badgeSize }} value={percentage} />
    </div>
  );
};
