'use client';

import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { pick } from 'ramda';
import type { ComponentProps } from 'react';

import { toast } from '@/components/toast';
import { type GetBotByIdQueryResult, getGetBotByIdQueryKey, getGetBotByIdQueryOptions, usePatchBot } from '@/lib/api';
import type { BotSummaryProps } from '@/module/bot-trading/bot-compare/bot-compare-summary';
import { AdvancedBotTradingSettings } from '@/module/bot-trading/components/advanced-settings/advanced-bot-trading-settings';

interface Props {
  botData: BotSummaryProps;
}

export const LaunchedBotAdvancedSettings = ({ botData }: Props) => {
  const t = useTranslations();
  const queryClient = useQueryClient();

  const { mutate: updateBotSettings } = usePatchBot({
    mutation: {
      onMutate: async (newSettings) => {
        await queryClient.cancelQueries(getGetBotByIdQueryOptions(botData.id));

        // snapshot the previous value so we can revert if mutation fails
        const previousBotData = queryClient.getQueryData(getGetBotByIdQueryKey(botData.id));

        // optimistically update to the new value
        queryClient.setQueryData(getGetBotByIdQueryKey(botData.id), {
          ...(previousBotData as GetBotByIdQueryResult),
          ...newSettings.data,
        });

        return { previousBotData, ...newSettings.data };
      },
    },
  });

  const handleToggleChange: ComponentProps<typeof AdvancedBotTradingSettings>['handleToggleChange'] = (
    settingKey,
    value,
  ) => {
    updateBotSettings(
      {
        botId: botData.id,
        data: { [settingKey]: value },
      },
      {
        onSuccess: () => toast.success(t('bot-trading.advanced-trading-settings.bot-settings-updated')),
        onError: (_error, _newSettings, context) => {
          // revert optimistic update on error
          queryClient.setQueryData(getGetBotByIdQueryKey(botData.id), context?.previousBotData);
          toast.error(t('bot-trading.advanced-trading-settings.update-failed'));
        },
        onSettled: () => void queryClient.invalidateQueries(getGetBotByIdQueryOptions(botData.id)),
      },
    );
  };

  return (
    <AdvancedBotTradingSettings
      handleToggleChange={handleToggleChange}
      settings={pick(
        [
          'tokenTickerCopyIsChecked',
          'creatorHighBuyIsChecked',
          'bundledBuysDetectedIsChecked',
          'suspiciousWalletsDetectedIsChecked',
          'shouldWaitBeforeBuying',
          'shouldAutoSellAfterHoldTime',
          'singleHighBuyIsChecked',
        ],
        botData,
      )}
    />
  );
};
