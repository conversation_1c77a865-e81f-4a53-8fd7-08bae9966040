import BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

import { AlertMessage, AlertMessageContainer } from '@/components/alert-message';
import { AmountInput } from '@/components/amount-input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { Button } from '@/components/ui/button';
import Display from '@/components/ui/display';
import { ROUTES } from '@/constants/routes';
import { Chain, useGetBotById } from '@/lib/api';
import { formatNumber } from '@/lib/formatters/format-number';
import { BotWalletItem } from '@/module/bot-trading/bot-withdraw/bot-wallet-item';
import { BotWithdrawGasFee } from '@/module/bot-trading/bot-withdraw/bot-withdraw-gas-fee';
import { WithdrawAddressInput } from '@/module/bot-trading/bot-withdraw/withdraw-address-input';
import { getChainConfig } from '@/module/multichain/get-chain-config';
import { BackLink } from '@/module/routing/back-link';

const usePresetOptions = () => {
  const t = useTranslations('bot-trading.bot-withdraw');

  return [
    { value: 10, label: '10%' },
    { value: 25, label: '25%' },
    { value: 50, label: '50%' },
    { value: 75, label: '75%' },
    { value: 100, label: t('all') },
  ];
};

export const WithdrawBotInputs = ({
  chain,
  botId,
  onAddressClick,
  tooltip,
  isSubmitting,
}: {
  botId: string;
  chain: Chain;
  onAddressClick: () => void;
  tooltip?: string;
  isSubmitting: boolean;
}) => {
  const form = useFormContext();
  const t = useTranslations('bot-trading.bot-withdraw');
  const percentageAmountToTransfer = form.watch('percentageAmountToTransfer') as number;
  const presetOptions = usePresetOptions();
  const { data: botData } = useGetBotById(botId);

  const destinationAddress = form.getValues('destinationAddress') as string;

  const {
    formState: { errors },
  } = form;

  const formattedWithdrawAmount = `${formatNumber(((percentageAmountToTransfer / 100) * Number(botData?.botWalletBalanceNativeAmount ?? '')).toString())} ${getChainConfig(Chain[chain]).symbol}`;
  const nativeWithdrawAmount = new BigNumber(botData?.botWalletBalanceNativeAmount ?? '').times(
    percentageAmountToTransfer / 100,
  );

  return (
    <>
      <div className="flex flex-col gap-1">
        <BackLink className="sm:hidden" defaultUrl={ROUTES.BOT_TRADING.DETAIL(botId)} />
        <div className="flex w-full justify-between">
          <Display size="L" weight="bold">
            {t('withdraw')} {getChainConfig(Chain[chain]).chainName}
          </Display>
        </div>
      </div>
      <div className="mt-2 flex h-full md:h-auto flex-col gap-2">
        <BotWalletItem amount={botData?.botWalletBalanceNativeAmount ?? ''} botId={botId} />

        <div className="relative">
          <WithdrawAddressInput onClick={onAddressClick} />
          {errors.destinationAddress ? (
            <AlertMessageContainer className="-mt-1 pt-1" variant="error">
              {/* eslint-disable-next-line @typescript-eslint/no-base-to-string -- TODO: Parse errors properly*/}
              <AlertMessage variant="error">{String(errors.destinationAddress.message)}</AlertMessage>
            </AlertMessageContainer>
          ) : null}
        </div>

        <AmountInput
          footer={formattedWithdrawAmount}
          name="percentageAmountToTransfer"
          presetOptions={presetOptions}
          suffix="%"
        />

        <BotWithdrawGasFee nativeAmount={nativeWithdrawAmount} recipientAddress={destinationAddress} />

        <Tooltip open={!!tooltip}>
          <TooltipTrigger asChild>
            <Button className="w-full block mt-auto mb-4 md:mt-0 md:mb-0" disabled={isSubmitting} type="submit">
              {t('withdraw')}
            </Button>
          </TooltipTrigger>
          <TooltipContent>{tooltip}</TooltipContent>
        </Tooltip>
      </div>
    </>
  );
};
