'use client';

import { Card } from '@/components/ui/card';
import { ROUTES } from '@/constants/routes';
import { BotMyTransactions } from '@/module/bot-trading/bot-compare/bot-my-transactions/bot-my-transactions';
import { BotDetailActivity } from '@/module/bot-trading/bot-detail/bot-detail-activity';
import { BotDetailPortfolioValue } from '@/module/bot-trading/bot-detail/bot-detail-portfolio-value';
import { BotDetailTrades } from '@/module/bot-trading/bot-detail/bot-detail-trades';
import { useBotActivityData } from '@/module/bot-trading/bot-detail/hooks/use-bot-activity-data';
import { useBotDetailData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-data';
import { BotDeleteSection } from '@/module/bot-trading/components/bot-delete-section';
import { getBotWalletFromBotDetail } from '@/module/bot-trading/utils/get-bot-wallet';

interface Props { botId: string }

export const BotDetailContent: React.FC<Props> = ({ botId }) => {
  const { botDetail, isLoading } = useBotDetailData({
    botId,
  });

  const botActivity = useBotActivityData(botId);

  return (
    <div className="grid grid-cols-1 items-start gap-5 pb-[100px] md:grid-cols-2 md:gap-2 lg:pb-0 xl:grid-cols-3">
      <Card className="gap-y-5" variant="area">
        <BotDetailPortfolioValue
          botName={botDetail?.botName ?? ''}
          botWalletAddress={botDetail?.botWalletAddress ?? ''}
          chartData={botDetail?.botPortfolioLastValues ?? []}
          isLoading={isLoading}
          totalPnlAmountFraction={botDetail?.botTotalPnlAmountFraction ?? '0'}
          totalPnlAmountUsd={botDetail?.botTotalPnlAmountUsd ?? '0'}
          totalValueAmountUsd={botDetail?.botTotalValueAmountUsd ?? '0'}
        />
        <BotMyTransactions
          botId={botId}
          data={botDetail?.botTransactions ?? []}
          isLoading={isLoading}
          redirectUrl={ROUTES.BOT_TRADING.DETAIL(botId)}
        />
      </Card>
      <Card variant="area">
        <BotDetailActivity
          botId={botId}
          botStatus={botDetail?.botStatus}
          botTotalValueAmountUsd={botDetail?.transactionsVolumeSum ?? ''}
          buyFrequency={botActivity?.buyFrequency ?? '0'}
          buyFrequencyLastResetAt={botActivity?.buyFrequencyLastResetAt ?? ''}
          buyTransactionsCount={botDetail?.buyTransactionsCount ?? 0}
          isActive={botDetail?.isActive ?? false}
          lossTransactionsCount={botDetail?.lossTransactionsCount ?? 0}
          profitableTransactionsCount={
            botDetail?.profitableTransactionsCount ?? 0
          }
          remainingBuyFrequency={botActivity?.remainingBuyFrequency ?? '0'}
          selectedWallet={getBotWalletFromBotDetail(botDetail)}
          walletAddress={botDetail?.botWalletAddress ?? ''}
        />
      </Card>
      <Card variant="area">
        <BotDetailTrades botId={botId} />
        <BotDeleteSection botId={botId} />
      </Card>
    </div>
  );
};
