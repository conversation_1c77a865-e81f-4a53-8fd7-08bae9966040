'use client';

import BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { Refresh } from '@/assets';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useRemainingTime } from '@/hooks/use-remaining-time';
import { cn } from '@/lib/utils';
import { useResetDailyLimit } from '@/module/bot-trading/hooks/use-reset-daily-limit';

interface Props {
  botId: string;
  lastResetTime: string;
  isActive: boolean;
  buyFrequency: string;
  remainingBuyFrequency: string;
}

export const BotDetailDailyLimit: React.FC<Props> = ({
  botId,
  lastResetTime,
  isActive,
  buyFrequency,
  remainingBuyFrequency,
}) => {
  const t = useTranslations('bot-trading');
  const timeRemaining = useRemainingTime(lastResetTime);
  const { handleResetLimit, isPending } = useResetDailyLimit(botId);

  const realizedTrades = new BigNumber(buyFrequency).minus(remainingBuyFrequency).toString();

  // cap to max 100%
  const tradesProgress = BigNumber.minimum(
    new BigNumber(realizedTrades).dividedBy(buyFrequency).multipliedBy(100),
    new BigNumber(100),
  ).toNumber();

  const buyCardinality = useMemo(
    () => (Number(realizedTrades) === 1 ? t('bot-detail.buy') : t('bot-detail.buys')),
    [realizedTrades, t],
  );

  return (
    <Card className="flex flex-col gap-2 shadow-primary" variant="primary">
      <div className="flex justify-between text-sm">
        <div className="flex items-center gap-1">
          <div className={cn('size-[14px] shrink-0 rounded-full bg-surface-brand-2')} />
          <p className="font-semibold capitalize text-text-elevated">{buyCardinality}</p>
          <p className="capitalize text-text-elevated">
            <span className="font-bold text-secondary-lighter">{realizedTrades}</span>/
            <span className="font-semibold ">{buyFrequency}</span>
          </p>
        </div>
        <div className="relative flex items-center space-x-1">
          {timeRemaining.formatted ? (
            <div className="font-semibold text-text-secondary">
              {timeRemaining.formatted} {t('bot-detail.left')}
            </div>
          ) : null}
          {(() => {
            const refreshButton = (
              <Button
                disabled={isPending || !isActive}
                size="icon-sm"
                variant="ghost"
                onClick={handleResetLimit}
              >
                <Refresh className={cn('size-3 shrink-0 text-text-secondary', isPending && 'animate-spin')} />
              </Button>
            );

            return !isActive ? (
              <Tooltip>
                <TooltipTrigger asChild className="flex">
                  {refreshButton}
                </TooltipTrigger>
                <TooltipContent>{t('bot-status.renew-trades-tooltip')}</TooltipContent>
              </Tooltip>
            ) : (
              refreshButton
            );
          })()}
        </div>
      </div>
      <Progress className="h-2 bg-surface-background shadow-primary-inner" value={tradesProgress} variant="glossy" />
    </Card>
  );
};
