'use client';

import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { parseAsBoolean, useQueryState } from 'nuqs';
import React from 'react';
import { match } from 'ts-pattern';

import { DataListHeader } from '@/components/data-list/data-list-header';
import { Skeleton } from '@/components/skeleton';
import { ROUTES } from '@/constants/routes';
import { usePathname, useRouter } from '@/i18n/routing';
import { isNativeToken } from '@/module/assets/utils';
import { useBotDetailData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-data';
import { isOpenedBotTrade } from '@/module/bot-trading/bot-detail/utils/is-opened-bot-trade';
import { sortBotTrades } from '@/module/bot-trading/bot-trades/utils/sort-bot-trades';
import { BotTradeRow } from '@/module/bot-trading/components/bot-trade-row';
import { TradeTabs } from '@/module/bot-trading/components/trades/trade-tabs';

interface Props {
  botId: string;
  redirectUrl?: string;
}

export const BotDetailTrades: React.FC<Props> = ({ botId, redirectUrl = ROUTES.BOT_TRADING.DETAIL(botId) }) => {
  const { botDetail, isLoading } = useBotDetailData({ botId });

  const [isBotMarketPositionActive] = useQueryState('isBotMarketPositionActive', parseAsBoolean.withDefault(true));

  const t = useTranslations('bot-trading.bot-trades');
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const rawData = match(isBotMarketPositionActive)
    .with(true, () => botDetail?.activeBotMarketPositions)
    .with(false, () => botDetail?.closedBotMarketPositions)
    .exhaustive();

  const sortedData = rawData ? sortBotTrades(rawData, isBotMarketPositionActive) : undefined;

  const hasData = !isLoading && !!sortedData?.length;

  return (
    <div className="grid grid-cols-1 gap-2">
      <DataListHeader link={ROUTES.BOT_TRADING.BOT_TRADES(botId, redirectUrl)} title={t('bot-trades')} />

      <TradeTabs />

      {isLoading ? (
        <>
          <Skeleton className="h-7.5 w-full rounded-xs shadow-primary" />
          <Skeleton className="h-7.5 w-full rounded-xs shadow-primary" />
        </>
      ) : null}

      {hasData ? (
        <div className="grid grid-cols-1 gap-2">
          {sortedData.map((item) => (
            <BotTradeRow
              key={item.id}
              botId={botId}
              botTradeId={item.id}
              chain={item.tokenChain}
              currentValueChangeFraction={item.pnlAmountFraction}
              currentValueChangeUsd={item.pnlAmountUsd}
              endTime={item.closedTimeStampAt}
              endValueUsd={item.closeValueUsd}
              image={item.tokenImageUrl}
              startTime={item.openTimeStampAt}
              startValueUsd={isOpenedBotTrade(item) ? item.currentValueUsd : item.openValueUsd}
              state={item.state}
              tokenDetailUrl={item.tokenDetailUrl}
              tokenName={item.tokenName}
              tokenSymbol={item.tokenSymbol}
              onClick={
                !isNativeToken(item)
                  ? () => {
                      router.push(
                        ROUTES.BOT_TRADING.BOT_TRADE_DETAIL(
                          botId,
                          item.id,
                          item.tokenChain,
                          item.tokenAddress,
                          pathname + '?' + searchParams.toString(),
                        ),
                      );
                    }
                  : undefined
              }
            />
          ))}
        </div>
      ) : null}

      {!isLoading && !hasData ? (
        <div className="text-center text-sm text-text-secondary">{t('no-bot-trades-found')}</div>
      ) : null}
    </div>
  );
};
