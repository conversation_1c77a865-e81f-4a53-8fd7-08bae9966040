import Link from 'next/link'
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Skeleton } from '@/components/skeleton';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from '@/i18n/routing';
import { useBotDetailData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-data';
import { useBotDetailSettingsData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-settings-data';
import { getBotWalletFromBotDetail } from '@/module/bot-trading/utils/get-bot-wallet';
import { WalletDepositDialog } from '@/module/wallet-detail/wallet-deposit-dialog';

import { WithdrawDeactivateBotDialog } from './withdraw-deactivate-bot-dialog';

interface Props {
  botId: string;
  botWalletAddress: string;
}

export const BotWithdrawAndDeposit: React.FC<Props> = ({ botId, botWalletAddress }) => {
  const router = useRouter();
  const t = useTranslations('bot-trading');
  const { isLoading } = useBotDetailSettingsData({ botId });
  const { refetchBotDetail } = useBotDetailData({ botId });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deactivateBotDialogOpen, setDeactivateBotDialogOpen] = useState(false);
  const { botDetail, isLoading: botDetailIsLoading } = useBotDetailData({
    botId,
  });

  const redirectUrl = ROUTES.BOT_TRADING.DETAIL(botId);

  if (isLoading || botDetailIsLoading) {
    return <Skeleton className="h-8 w-full rounded-full" data-testid="loading-skeleton" />;
  }

  const handleDialogChange = (value: boolean) => {
    setDialogOpen(value);
    if (!value) {
      // refetch bot detail after deposit dialog is closed
      void refetchBotDetail();
    }
  };

  return (
    <div className="flex items-center gap-2" data-testid="sufficient-balance-container">
      <WalletDepositDialog
        open={dialogOpen}
        selectedWallet={getBotWalletFromBotDetail(botDetail)}
        walletAddress={botWalletAddress}
        onOpenChange={handleDialogChange}
      >
        <Button className="flex-1 px-0" data-testid="deposit-button">
          {t('bot-detail.deposit')}
        </Button>
      </WalletDepositDialog>

      {botDetail?.isActive ? (
        <WithdrawDeactivateBotDialog
          botAvatar={botDetail.botAvatarFileId}
          botId={botId}
          open={deactivateBotDialogOpen}
          onOpenChange={setDeactivateBotDialogOpen}
          onSuccess={() => {
            router.push(ROUTES.BOT_TRADING.BOT_WITHDRAW(botId, redirectUrl));
          }}
        >
          <Button
            className="flex-1"
            data-testid="withdraw-button"
            variant="outline"
            onClick={() => { setDeactivateBotDialogOpen(true); }}
          >
            {t('bot-detail.withdraw')}
          </Button>
        </WithdrawDeactivateBotDialog>
      ) :
        <Link className="flex-1" href={ROUTES.BOT_TRADING.BOT_WITHDRAW(botId, redirectUrl)}>
          <Button data-testid="withdraw-button" variant="outline">
            {t('bot-detail.withdraw')}
          </Button>
        </Link>

      }
    </div>
  );
};
