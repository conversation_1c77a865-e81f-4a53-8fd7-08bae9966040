import { useTranslations } from 'next-intl';
import { useQueryStates } from 'nuqs';
import type { ReactNode } from 'react';

import { toast } from '@/components/toast';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import Display from '@/components/ui/display';
import {
  getGetBotByIdQueryOptions,
  getGetMyBotsQueryOptions,
  getSearchUserBotDetailQueryOptions,
  TimeRange,
  useUpdateBotState,
} from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { BotAvatar } from '@/module/bot-trading/components/bot-avatar';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { parseTimeRange } from '@/module/bot-trading/utils/timerange-utils';

export const WithdrawDeactivateBotDialog = ({
  botAvatar,
  botId,
  children,
  onOpenChange,
  onSuccess,
  open,
  hideClose,
}: {
  open: boolean;
  botAvatar: string;
  botId: string;
  onOpenChange?: (open: boolean) => void;
  children?: ReactNode;
  onSuccess?: () => void;
  hideClose?: boolean;
}) => {
  const t = useTranslations('bot-trading');
  const queryClient = getQueryClient();

  const [{ timeRange }] = useQueryStates({
    timeRange: {
      parse: parseTimeRange,
      defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    },
  });

  const { mutate: updateBotState, isPending } = useUpdateBotState({
    mutation: {
      onSuccess: () => {
        void queryClient.invalidateQueries(
          getSearchUserBotDetailQueryOptions({
            botId,
            timeRange: timeRange,
          }),
        );
        toast.success(t('bot-toggle.bot-status-updated'));
        void queryClient.invalidateQueries(getGetMyBotsQueryOptions({ timeRange: TimeRange.ALL }));
        void queryClient.invalidateQueries(getGetBotByIdQueryOptions(botId));
        onSuccess?.();
      },
      onError: () => {
        toast.error(t('bot-toggle.bot-status-update-failed'));
      },
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children ? <DialogTrigger asChild>{children}</DialogTrigger> : null}
      <DialogContent
        bgVariant="secondary"
        className="!sm:min-w-[450px] sm:gap-5 flex items-center"
        hideClose={hideClose}
      >
        <BotAvatar avatarFileId={botAvatar} width={203} />
        <DialogTitle className="text-center flex flex-col gap-2">
          <Display size="M" tag="span" weight="bold">
            {t('bot-detail.pause-bot-dialog.title')}
          </Display>
          <DialogDescription className="!text-text-secondary text-center font-semibold size-md">
            {t('bot-detail.pause-bot-dialog.text')}
          </DialogDescription>
        </DialogTitle>

        <Button
          className="w-full"
          disabled={isPending}
          onClick={() => {
            updateBotState({ botId, params: { active: false } });
          }}
        >
          {t('bot-detail.pause-bot-dialog.pause-and-withdraw')}
        </Button>
      </DialogContent>
    </Dialog>
  );
};
