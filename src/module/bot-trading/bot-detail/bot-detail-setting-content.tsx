'use client';

import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { ErrorMessage } from '@/components/error-message';
import { Loader } from '@/components/ui/loader';
import { ROUTES } from '@/constants/routes';
import { useBotDetailSettingsData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-settings-data';
import { BotSummary } from '@/module/bot-trading/bot-wizard/components/bot-summary';

export const BotDetailSettingContent: React.FC = () => {
  const t = useTranslations();

  const { botId } = useParams<{ botId: string }>();
  const { botDetailSettings, isLoading, isError } = useBotDetailSettingsData({
    botId,
  });

  if (isError) {
    return (
      <ErrorMessage className="flex h-full items-center justify-center" variant="error">
        {t('common.something-went-wrong')}
      </ErrorMessage>
    );
  }

  if (isLoading) {
    return <Loader />;
  }

  if (!botDetailSettings) {
    return (
      <div className="flex h-full items-center justify-center">
        {t('bot-trading.bot-detail.no-bot-detail-settings-found')}
      </div>
    );
  }

  return (
    <div className="w-full pb-[100px] lg:pb-0">
      <BotSummary botData={botDetailSettings} redirectUrl={ROUTES.BOT_TRADING.SETTINGS(botDetailSettings.id)} />
    </div>
  );
};
