import { ChevronDownIcon, ChevronUpIcon } from '@radix-ui/react-icons';
import { useState } from 'react';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/form/select';
import { Card } from '@/components/ui/card';
import type { CompareBotsResult, GetMyBotsResult } from '@/lib/api';

interface Props {
  selectedBot: CompareBotsResult | GetMyBotsResult;
  availableBots: (CompareBotsResult | GetMyBotsResult)[];
  setSelectedBot: (botId: string) => void;
  disabledIds?: string[];
}

interface BotProps {
  id: string;
  name: string;
}

const getBotProps = (bot: CompareBotsResult | GetMyBotsResult): BotProps => {
  if ('id' in bot) {
    return {
      id: bot.id,
      name: bot.name ?? '',
    };
  }
  return {
    id: bot.botId,
    name: bot.botName,
  };
};

export const BotCompareSelect: React.FC<Props> = ({
  selectedBot,
  setSelectedBot,
  availableBots,
  disabledIds = [],
}) => {
  const [open, setOpen] = useState(false);

  const { id, name } = getBotProps(selectedBot);

  return (
    <Select
      open={open}
      value={id}
      onOpenChange={setOpen}
      onValueChange={setSelectedBot}
    >
      <SelectTrigger className="select-none border-none p-0" hideIndicator>
        <Card
          className="flex w-full flex-row items-center justify-between border-none shadow-primary"
          variant="primary"
        >
          <span className="truncate text-md font-semibold text-text-secondary">
            {name}
          </span>
          {open ? (
            <ChevronUpIcon className="size-3" />
          ) : (
            <ChevronDownIcon className="size-3" />
          )}
        </Card>
      </SelectTrigger>
      <SelectContent>
        {availableBots.map((option) => {
          const { id, name } = getBotProps(option);

          return (
            <SelectItem key={id} disabled={disabledIds.includes(id)} value={id}>
              <div className="max-w-40 truncate text-md">{name}</div>
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
};
