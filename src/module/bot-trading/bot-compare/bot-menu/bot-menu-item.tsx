import Link from 'next/link';
import type { ReactNode } from 'react';

import { ChevronRight, ExternalLinkIcon } from '@/assets';

export interface BotMenuItemProps {
  href: string;
  label: ReactNode;
  value: ReactNode;
  valueSubscript?: string;
  isExternal?: boolean;
}

export const BotMenuItem = ({
  href,
  isExternal,
  label,
  value,
  valueSubscript,
}: BotMenuItemProps) => (
    <li className="mb-2 flex size-full cursor-pointer rounded-sm bg-surface-primary shadow-primary hover:bg-surface-secondary/30">
      <Link
        href={href}
        {...(isExternal && {
          target: '_blank',
          rel: 'noopener noreferrer',
        })}
        className="flex w-full items-center justify-start gap-1 p-2"
      >
        <div className="flex flex-col text-md text-text-secondary">
          <span className="font-semibold">{label}</span>
          {valueSubscript ? <span className="text-sm">{valueSubscript}</span> : null}
        </div>
        <span className="ml-auto  text-md font-bold text-white">{value}</span>
        <div className="flex items-center gap-0.5">
          {isExternal ? (
            <ExternalLinkIcon className="size-3 shrink-0 text-border-secondary" />
          ) : (
            <ChevronRight className="shrink-0 text-border-secondary" />
          )}
        </div>
      </Link>
    </li>
  );
