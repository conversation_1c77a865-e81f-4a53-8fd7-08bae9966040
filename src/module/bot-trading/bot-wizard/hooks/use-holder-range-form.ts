import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useGetBotSettingsStatistics } from '@/lib/api';
import {
  normalizeEndSliderValue,
  normalizeStartSliderValue,
  validateSliderValues,
} from '@/module/bot-trading/bot-wizard/utils/slider-values-utils';

import { useBotWizardApi } from './use-bot-wizard-api';
import { useBotWizardFormData } from './use-bot-wizard-form-data';
import { useBotWizardStep } from './use-bot-wizard-step';
import { useExitWizard } from './use-exit-wizard';

interface Step15FormData {
  numberOfHolders: string[];
}

export const useHolderRangeForm = () => {
  const t = useTranslations('bot-trading.create-bot.step-15');
  const { next } = useBotWizardStep();
  const [formData, setFormData] = useBotWizardFormData();
  const { data, isLoading } = useGetBotSettingsStatistics();
  const { apiUpdateBot } = useBotWizardApi();
  const { handleExitWizard } = useExitWizard();

  const schema = z.object({
    numberOfHolders: z
      .array(z.string())
      .length(2)
      .refine(validateSliderValues, {
        path: ['[0]'],
        message: t('validation'),
      }),
  });

  const form = useForm<Step15FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      numberOfHolders: [formData.numberOfHoldersFrom, formData.numberOfHoldersTo],
    },
  });

  const errorFrom = form.formState.errors.numberOfHolders?.[0]?.message;
  const errorTo = form.formState.errors.numberOfHolders?.[1]?.message;

  const saveData = async (values: Step15FormData) => {
    await setFormData((preValue) => ({
      ...preValue,
      numberOfHoldersFrom: values.numberOfHolders[0],
      numberOfHoldersTo: values.numberOfHolders[1],
    }));

    await apiUpdateBot({
      numberOfHoldersFrom: normalizeStartSliderValue(values.numberOfHolders[0]),
      numberOfHoldersTo: normalizeEndSliderValue(values.numberOfHolders[1]),
    });
  };

  const handleNextStep = async (values: Step15FormData) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }
    await saveData(values);
    await next();
  };

  const handleExit = async (values: Step15FormData) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }
    await saveData(values);
    handleExitWizard();
  };

  return {
    form,
    handleNextStep,
    handleExit,
    numberOfHoldersData: data?.numberOfHolders ?? [],
    isLoading,
    error: errorFrom || errorTo,
  };
};
