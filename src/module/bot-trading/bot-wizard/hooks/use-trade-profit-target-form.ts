/* eslint-disable no-magic-numbers */
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useGetBotSettingsStatistics } from '@/lib/api';
import { useExitWizard } from '@/module/bot-trading/bot-wizard/hooks/use-exit-wizard';
import { validatePositiveSliderValues } from '@/module/bot-trading/bot-wizard/utils/slider-values-utils';

import { useBotWizardApi } from './use-bot-wizard-api';
import { useBotWizardFormData } from './use-bot-wizard-form-data';
import { useBotWizardStep } from './use-bot-wizard-step';

interface Step9FormData {
  profitTargetFraction: string[];
}

// left value is PERCENTAGE so we need to divide by 100
const PROFIT_TARGET_SMALL_FROM = 0;
const PROFIT_TARGET_SMALL_TO = 3 / 100;
const PROFIT_TARGET_NORMAL_FROM = 3 / 100;
const PROFIT_TARGET_NORMAL_TO = 10 / 100;
const PROFIT_TARGET_LARGE_FROM = 10 / 100;

export const useTradeProfitTargetForm = () => {
  const t = useTranslations('bot-trading.create-bot.step-9');
  const { next } = useBotWizardStep();
  const [formData, setFormData] = useBotWizardFormData();
  const { data, isLoading } = useGetBotSettingsStatistics();
  const { apiUpdateBot } = useBotWizardApi();
  const { handleExitWizard } = useExitWizard();

  const profitTargetValidationConfig = {
    smallAmount: {
      from: PROFIT_TARGET_SMALL_FROM,
      to: PROFIT_TARGET_SMALL_TO,
      message: t('amount-too-small'),
    },
    normalAmount: {
      from: PROFIT_TARGET_NORMAL_FROM,
      to: PROFIT_TARGET_NORMAL_TO,
      message: t('amount-normal'),
    },
    largeAmount: {
      from: PROFIT_TARGET_LARGE_FROM,
      message: t('amount-too-large'),
    },
  };

  const schema = z.object({
    profitTargetFraction: z
      .array(z.string())
      .refine((array) => array.length > 0, {
        message: t('validation-required'),
        path: ['[0]'],
      })
      .refine(validatePositiveSliderValues, {
        path: ['[0]'],
        message: t('validation'),
      }),
  });

  const form = useForm<Step9FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      profitTargetFraction: [formData.profitTargetFraction],
    },
  });

  const error = form.formState.errors.profitTargetFraction?.[0]?.message;

  const saveData = async (values: Step9FormData) => {
    await setFormData((preValue) => ({
      ...preValue,
      profitTargetFraction: values.profitTargetFraction[0]?.toString(),
    }));
    await apiUpdateBot({
      profitTargetFraction: values.profitTargetFraction[0]?.toString(),
    });
  };

  const handleNextStep = async (values: Step9FormData) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }
    await saveData(values);
    await next();
  };

  const handleExit = async (values: Step9FormData) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }
    await saveData(values);
    handleExitWizard();
  };

  return {
    form,
    handleNextStep,
    handleExit,
    profitTargetData: data?.profitTarget ?? [],
    profitTargetValidationConfig,
    isLoading,
    error,
  };
};
