/* eslint-disable no-nested-ternary -- TODO: Get rid off disgusting nested ternary expression */
'use client';

import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { useRef } from 'react';

import { EditIcon } from '@/assets';
import { InfoCard } from '@/components/info-card';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useRouter } from '@/i18n/routing';
import type { GetBotResult } from '@/lib/api';
import { cn } from '@/lib/utils';
import { SummaryItemCard } from '@/module/bot-trading/bot-wizard/components/summary-item-card';
import { SummaryPriorityCard } from '@/module/bot-trading/bot-wizard/components/summary-priority-card';
import { SummaryWalletCard } from '@/module/bot-trading/bot-wizard/components/summary-wallet-card';
import { SUMMARY_SECTION_DATA } from '@/module/bot-trading/bot-wizard/constants';
import { SummaryAdvancedSettings } from '@/module/bot-trading/bot-wizard/steps/summary/summary-advanced-settings';
import { isBotDraftReady } from '@/module/bot-trading/bot-wizard/utils/is-bot-draft-ready';
import { prepareBotWizardUrl } from '@/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url';
import { LaunchedBotAdvancedSettings } from '@/module/bot-trading/components/advanced-settings/launched-bot-advanced-settings';
import { BotAvatar } from '@/module/bot-trading/components/bot-avatar';
import { BotDeleteSection } from '@/module/bot-trading/components/bot-delete-section';

import { EDIT_BOT_BUTTON_ID } from '../utils/bot-summary-element-ids';

import { BotSummaryCopySettings } from './bot-summary-copy-settings';

const AVATAR_EDIT_STEP = 3;

type BotSummaryProps = Partial<
  Omit<GetBotResult, 'buyFrequencyLastResetAt' | 'remainingBuyFrequency' | 'userReadableId'>
> & {
  id: string;
};

interface Props {
  botData: BotSummaryProps;
  redirectUrl?: string;
  className?: string;
}

export const BotSummary: React.FC<Props> = ({ botData, redirectUrl, className }) => {
  const router = useRouter();
  const settingsRef = useRef<HTMLDivElement>(null);

  const t = useTranslations('bot-trading.create-bot');
  const [botDraftId] = useQueryState('botDraftId');
  const botDraftReadyToLaunch = isBotDraftReady(botData);
  const isLaunchedBot = !!botData.botWalletAddress;

  const handleSummaryItemClick = (step: number) => {
    const url = prepareBotWizardUrl({
      ...(isLaunchedBot && { botId: botData.id }),
      ...(botDraftId && { botDraftId }),
      botData,
      redirectUrl,
      step,
    });

    // set full wizard URL
    router.push(url);
  };

  return (
    <div
      ref={settingsRef}
      className={cn(
        'grid items-start gap-3 md:grid-cols-2 md:gap-2 xl:grid-cols-3 bg-surface-background -m-3 p-3',
        className,
      )}
    >
      <Card className="contents gap-4 md:flex" variant="area">
        <div className="relative flex items-center justify-center">
          {botData.avatarFileId ? <BotAvatar avatarFileId={botData.avatarFileId} className="max-h-[160px]" /> : null}
          <Button
            className="absolute -bottom-2 shadow-primary"
            id={EDIT_BOT_BUTTON_ID}
            size="icon"
            variant="secondary"
            onClick={() => {
              handleSummaryItemClick(AVATAR_EDIT_STEP);
            }}
          >
            <EditIcon className="size-3" />
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center gap-0.5">
          <div className="text-display-m font-bold">{botDraftId ? t('bot-summary.title') : t('bot-setting.title')}</div>
          <div className="text-md font-semibold text-text-secondary">{t('bot-summary.description')}</div>
        </div>

        {botDraftId ? (
          botDraftReadyToLaunch ? (
            <InfoCard title={t('bot-summary.success-caption')} variant="success" />
          ) : (
            <InfoCard title={t('bot-summary.warning-caption')} variant="info" />
          )
        ) : null}

        <div className="flex flex-col gap-2">
          <div className="text-display-xs font-bold">{t('bot-summary.basics')}</div>
          {SUMMARY_SECTION_DATA.basics.map((item) => (
            <div
              key={item.value}
              className="cursor-pointer"
              onClick={() => {
                handleSummaryItemClick(item.step);
              }}
            >
              <SummaryItemCard key={item.value} {...item} value={botData[item.value]} />
            </div>
          ))}
          {botData.botWalletAddress && botData.botWalletBalanceUsd ? (
            <SummaryWalletCard walletAddress={botData.botWalletAddress} walletBalance={botData.botWalletBalanceUsd} />
          ) : null}
        </div>

        <BotSummaryCopySettings settingsContainerRef={settingsRef} />
      </Card>

      <div className="flex flex-col gap-2">
        <Card className="flex gap-2" variant="area">
          <h3 className="text-display-xs font-bold">{t('bot-summary.strategy')}</h3>
          {SUMMARY_SECTION_DATA.strategy.map((item) => (
            <div
              key={item.value}
              className="cursor-pointer"
              onClick={() => {
                handleSummaryItemClick(item.step);
              }}
            >
              <SummaryItemCard {...item} value={botData[item.value]} />
            </div>
          ))}
          {botDraftId ? <SummaryPriorityCard className="hidden sm:flex" /> : null}

          <div className="flex items-center justify-between">
            <h3 className="text-display-xs font-bold">{t('bot-summary.targets')}</h3>
            <div className="rounded-full bg-primary-alphaLight px-1.5 py-0.5 text-xxs font-semibold uppercase text-text-active">
              {t('optional.title')}
            </div>
          </div>
          {SUMMARY_SECTION_DATA.targets.map((item) => (
            <div
              key={`${item.valueFrom}-${item.valueTo}`}
              className="cursor-pointer"
              onClick={() => {
                handleSummaryItemClick(item.step);
              }}
            >
              <SummaryItemCard {...item} valueFrom={botData[item.valueFrom]} valueTo={botData[item.valueTo]} />
            </div>
          ))}
          {botData.id ? <SummaryPriorityCard className="flex sm:hidden" /> : null}
        </Card>
      </div>
      <Card className="flex flex-col gap-4" variant="area">
        <h3 className="text-display-xs font-bold">{t('fat-shield')}</h3>
        <div className="space-y-2">
          {isLaunchedBot ? <LaunchedBotAdvancedSettings botData={botData} /> : <SummaryAdvancedSettings />}
          <BotDeleteSection botId={botData.id} />
        </div>
      </Card>
    </div>
  );
};
