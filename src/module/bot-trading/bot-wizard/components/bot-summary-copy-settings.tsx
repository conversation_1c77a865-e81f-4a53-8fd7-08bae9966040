'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { type FC } from 'react';

import { CopyIcon, Download01 } from '@/assets';
import { toast } from '@/components/toast';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import Display from '@/components/ui/display';
import { EXTERNAL_LINKS } from '@/constants/external-links';
import { copyHtmlElementAsImage } from '@/lib/dom/copy-html-element-as-image';
import { exportHtmlElement } from '@/lib/dom/export-html-element';

import { EDIT_BOT_BUTTON_ID, COPY_SETTINGS_ID, DELETE_BOT_BUTTON_ID } from '../utils/bot-summary-element-ids';

interface Props {
  settingsContainerRef: React.RefObject<HTMLDivElement | null>;
}

const filter = (node: Node) => {
  if (node instanceof HTMLElement) {
    return ![EDIT_BOT_BUTTON_ID, DELETE_BOT_BUTTON_ID].includes(node.id);
  }

  return true;
};

const BotSummaryCopySettings: FC<Props> = ({ settingsContainerRef }) => {
  const t = useTranslations('bot-trading.create-bot.copy-settings');
  const tProfile = useTranslations('profile');

  const handleDownloadSettings = async () => {
    await exportHtmlElement(settingsContainerRef.current, 'bot-settings', 'png', {
      filter,
    });
  };

  const handleCopySettings = async () => {
    await copyHtmlElementAsImage(settingsContainerRef.current, {
      filter,
      onSuccess: () => {
        toast.success(t('copied-to-clipboard'));
      },
      onError: () => {
        toast.error(t('failed-to-copy'));
      },
    });
  };

  return (
    <Card
      className="flex flex-col gap-3 p-2 pb-3 @container/copySettings shadow-primary"
      id={COPY_SETTINGS_ID}
      variant="primary"
    >
      <div className="space-y-1">
        <Display size="XS" weight="bold">
          {t('title')}
        </Display>
        <Display className="text-text-secondary text-base font-semibold text-pretty xl:pr-10" tag="p">
          {t('description')}
          <Link className="underline" href={EXTERNAL_LINKS.DISCORD_SUPPORT} rel="noopener noreferrer" target="_blank">
            {tProfile('discord')}
          </Link>{' '}
          &{' '}
          <Link className="underline" href={EXTERNAL_LINKS.TELEGRAM_SUPPORT} rel="noopener noreferrer" target="_blank">
            {tProfile('telegram')}
          </Link>
        </Display>
      </div>

      <div className="grid grid-cols-1 gap-x-1 gap-y-2 @[380px]/copySettings:grid-cols-2">
        <Button className="px-2" variant="primary" onClick={handleCopySettings}>
          <span className="text-md font-bold text-text-button">{t('copy')}</span>
          <CopyIcon className="size-3 text-black shrink-0" />
        </Button>
        <Button className="px-2" variant="outline" onClick={handleDownloadSettings}>
          <span className="text-md font-bold">{t('download')}</span>
          <Download01 className="size-3 text-primary shrink-0" />
        </Button>
      </div>
    </Card>
  );
};

export { COPY_SETTINGS_ID, BotSummaryCopySettings };
