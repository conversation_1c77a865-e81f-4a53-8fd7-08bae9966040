import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { type FC, useState, useTransition } from 'react';

import { toast } from '@/components/toast';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import Display from '@/components/ui/display';
import { LoadingButton } from '@/components/ui/loading-button';
import { getGetAllBotDraftsQueryOptions, getGetMyBotsQueryOptions, useDeleteBot, useDeleteBotDraft } from '@/lib/api';
import { formatRichText } from '@/lib/formatters/format-rich-text';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { parseTimeRange, TIME_RANGE_QUERY_PARAM } from '@/module/bot-trading/utils/timerange-utils';

import { DELETE_BOT_BUTTON_ID } from '../utils/bot-summary-element-ids';

interface Props {
  botId: string;
  // if bot has a balance, it means it's a launched bot
  botBalance: number | null;
  isActive?: boolean;
  onDelete?: () => void;
}

export const BotDeleteDialog: FC<Props> = ({ botId, botBalance, isActive, onDelete }) => {
  const [isOpen, setIsOpen] = useState(false);
  const queryClient = useQueryClient();
  const t = useTranslations('bot-trading.create-bot');
  const [isPending, startTransition] = useTransition();

  const [timeRange] = useQueryState(TIME_RANGE_QUERY_PARAM, {
    defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    parse: parseTimeRange,
  });

  const canDeleteBot = botBalance == null || (!isActive && botBalance === 0);

  const { mutateAsync: deleteBotDraft } = useDeleteBotDraft();
  const { mutateAsync: deleteBot } = useDeleteBot();

  const onConfirm = () => {
    startTransition(async () => {
      try {
        if (botBalance != null) {
          await deleteBot({ botId });
        } else {
          await deleteBotDraft({ botDraftId: botId });
        }

        await Promise.all([
          queryClient.invalidateQueries(getGetAllBotDraftsQueryOptions()),
          queryClient.invalidateQueries(getGetMyBotsQueryOptions({ timeRange })),
        ]);

        onDelete?.();
        setIsOpen(false);
        toast.success(t('bot-summary.delete-bot-dialog.success'));
      } catch {
        toast.error(t('bot-summary.delete-bot-dialog.error'));
      }
    });
  };

  const renderDeleteButton = (canDeleteBot: boolean) => {
    if (!canDeleteBot) {
      return (
        <Tooltip>
          <TooltipTrigger>
            <span
              className="whitespace-nowrap text-md font-semibold capitalize text-text-secondary transition-colors hover:cursor-default opacity-50 hover:text-text-secondary"
              id={DELETE_BOT_BUTTON_ID}
            >
              {t('bot-summary.delete-bot')}
            </span>
          </TooltipTrigger>
          <TooltipContent align="end" side="bottom">
            <span>{t.rich('bot-summary.delete-bot-dialog.tooltip', formatRichText)}</span>
          </TooltipContent>
        </Tooltip>
      );
    }

    return (
      <DialogTrigger
        disabled={isPending}
        onClick={() => {
          setIsOpen(true);
        }}
      >
        <span className="whitespace-nowrap text-md font-semibold capitalize text-text-secondary transition-colors hover:text-text-primary">
          {t('bot-summary.delete-bot')}
        </span>
      </DialogTrigger>
    );
  };

  const isBotDraft = botBalance === null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {renderDeleteButton(canDeleteBot)}
      <DialogContent className="w-fit bg-surface-primary sm:gap-2" hideClose={true}>
        <DialogTitle>
          <Display size="S" tag="span">
            {t(`bot-summary.delete-bot-dialog.${isBotDraft ? 'draft' : 'launched-bot'}-title`)}
          </Display>
        </DialogTitle>
        <span className="mb-1  whitespace-pre-line font-medium text-text-secondary">
          {t(`bot-summary.delete-bot-dialog.${isBotDraft ? 'draft' : 'launched-bot'}-description`)}
        </span>

        <DialogDescription />

        <DialogClose asChild>
          <Button className="w-full" variant="outline">
            {t('bot-summary.delete-bot-dialog.cancel')}
          </Button>
        </DialogClose>
        <LoadingButton isLoading={isPending} onClick={onConfirm}>
          {t('bot-summary.delete-bot-dialog.confirm')}
        </LoadingButton>
      </DialogContent>
    </Dialog>
  );
};
