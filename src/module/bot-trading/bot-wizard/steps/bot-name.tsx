import { useTranslations } from 'next-intl';
import { memo } from 'react';

import { User } from '@/assets/user';
import { AlertErrorMessage } from '@/components/alert-message';
import { Form, FormControl, FormField, FormItem } from '@/components/form/form';
import { Input } from '@/components/form/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { cn } from '@/lib/utils';
import { BotWizardAppHeader } from '@/module/bot-trading/bot-wizard/components/bot-wizard-app-header';
import { BotWizardProcessBar } from '@/module/bot-trading/bot-wizard/components/bot-wizard-process-bar';
import { NextStepButton } from '@/module/bot-trading/bot-wizard/components/next-step-button';
import { MAX_BOTS_ALLOWED } from '@/module/bot-trading/bot-wizard/constants';
import { useBotNameForm } from '@/module/bot-trading/bot-wizard/hooks/use-bot-name-form';
import { useBotWizardFormData } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-form-data';
import { BotAvatar } from '@/module/bot-trading/components/bot-avatar';

const FORM_ID = 'step-4-form';

export const BotName = memo(() => {
  const t = useTranslations('bot-trading.create-bot');
  const { form, handleNextStep, handleExit, isCreateBotEnabled } = useBotNameForm();
  const [formData] = useBotWizardFormData();

  return (
    <>
      <BotWizardAppHeader onExit={() => void handleExit(form.getValues())} />
      <div className="flex flex-1 flex-col gap-4 overflow-auto">
        <div className="flex items-center justify-center whitespace-pre-line text-display-l font-bold">
          <div className="text-display-m font-bold sm:text-display-xxl [@media(min-height:844px)]:pt-4">
            {t('step-4.title')}
          </div>
        </div>
        <div className="my-5 flex items-center justify-center sm:my-0 md:[@media(min-height:844px)]:my-5">
          {formData.avatarFileId ? (
            <BotAvatar
              avatarFileId={formData.avatarFileId}
              className="h-[30dvh] max-h-[270px] sm:size-full sm:max-h-[300px]"
              height={300}
              width={300}
            />
          ) : null}
        </div>
        <div className="sticky bottom-0 flex items-center justify-center">
          <Form {...form}>
            <form
              className="flex w-full max-w-[350px] flex-col gap-3"
              id={FORM_ID}
              onSubmit={form.handleSubmit(handleNextStep)}
            >
              <div className="flex flex-col gap-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          icon={<User className="text-text-secondary" />}
                          placeholder={t('step-4.form.bot-name')}
                          {...field}
                          autoComplete="off"
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <AlertErrorMessage className="-mt-3" inputName="name" />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </div>
      </div>

      <div>
        <BotWizardProcessBar />
        <div className="wizard-action-wrapper">
          <Tooltip open={!isCreateBotEnabled}>
            <TooltipTrigger asChild className="flex-1 sm:flex-none">
              <span className="w-full sm:w-fit">
                <NextStepButton
                  className={cn({
                    'pointer-events-none cursor-not-allowed opacity-50': !isCreateBotEnabled,
                  })}
                  form={FORM_ID}
                />
              </span>
            </TooltipTrigger>

            <TooltipContent>
              {t('maximum-bots', {
                count: MAX_BOTS_ALLOWED,
              })}
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </>
  );
});

BotName.displayName = 'BotName';
