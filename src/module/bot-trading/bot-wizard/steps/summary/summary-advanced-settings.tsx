import { useTranslations } from 'next-intl';
import { parseAsString, useQueryState } from 'nuqs';
import { pick } from 'ramda';
import { match, P } from 'ts-pattern';

import { toast } from '@/components/toast';
import { useBotDetailSettingsData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-settings-data';
import { useBotWizardApi } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-api';
import { AdvancedBotTradingSettings } from '@/module/bot-trading/components/advanced-settings/advanced-bot-trading-settings';
import { AdvancedSettingsSkeleton } from '@/module/bot-trading/components/advanced-settings/advanced-settings-skeleton';
import { LaunchedBotAdvancedSettings } from '@/module/bot-trading/components/advanced-settings/launched-bot-advanced-settings';

export const SummaryAdvancedSettings = () => {
  const t = useTranslations();
  const { botDrafts, apiUpdateBot, isLoading: isBotWizardLoading } = useBotWizardApi();
  const [botDraftId] = useQueryState('botDraftId', parseAsString.withDefault(''));
  const [botId] = useQueryState('botId', parseAsString.withDefault(''));
  const botDraft = botDrafts?.find((draft) => draft.id === botDraftId);

  const { botDetailSettings, isLoading: isBotDetailSettingsLoading } = useBotDetailSettingsData({
    botId,
  });

  return match({ botDetailSettings, botDraft, isBotDetailSettingsLoading, isBotWizardLoading })
    .with(
      P.when(({ isBotWizardLoading, isBotDetailSettingsLoading }) => isBotWizardLoading || isBotDetailSettingsLoading),
      () => <AdvancedSettingsSkeleton />,
    )
    .with({ botDetailSettings: P.not(P.nullish) }, ({ botDetailSettings }) => (
      <LaunchedBotAdvancedSettings botData={botDetailSettings} />
    ))
    .with({ botDraft: P.not(P.nullish) }, ({ botDraft }) => (
      <AdvancedBotTradingSettings
        handleToggleChange={async (settingKey, value) => {
          await apiUpdateBot({ [settingKey]: value }, () =>
            toast.info(t('bot-trading.advanced-trading-settings.bot-settings-updated')),
          );
        }}
        settings={pick(
          [
            'tokenTickerCopyIsChecked',
            'creatorHighBuyIsChecked',
            'bundledBuysDetectedIsChecked',
            'suspiciousWalletsDetectedIsChecked',
            'shouldWaitBeforeBuying',
            'shouldAutoSellAfterHoldTime',
            'singleHighBuyIsChecked',
          ],
          botDraft,
        )}
      />
    ))
    .otherwise(() => null);
};
