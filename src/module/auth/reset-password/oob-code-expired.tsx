import { getTranslations } from 'next-intl/server';

import { ForgotPasswordForm } from './reset-password-form';

export const OobCodeExpired = async ({
  hasOobCode,
}: {
  hasOobCode: boolean;
}) => {
  const t = await getTranslations('auth-error-messages');

  return (
    <div className="flex flex-col gap-2">
      {hasOobCode ? <div className="text-center text-base text-text-secondary">
          {t('expired-link')}
        </div> : null}
      <ForgotPasswordForm />
    </div>
  );
};
