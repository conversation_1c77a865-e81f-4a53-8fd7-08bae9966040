import BigNumber from 'bignumber.js';

import type { Chain } from '@/lib/api';
import { formatNumber } from '@/lib/formatters/format-number';
import { formatUsd } from '@/lib/formatters/format-usd';
import { cn } from '@/lib/utils';
import { TokenAvatar } from '@/module/manual-trading/components/token-avatar';

interface Props {
  radius?: 'rounded-xs' | 'rounded-xxs';
  imageUrl?: string;
  name?: string;
  amount?: string;
  price?: string;
  chain: Chain;
}

export const WalletTokenItem = ({ radius = 'rounded-xxs', imageUrl, name, amount, price, chain }: Props) => (
  <>
    <div className={cn('flex size-5 items-center justify-center bg-surface-third-layer', radius)}>
      <TokenAvatar chain={chain} imageUrl={imageUrl} name={name ?? ''} />
    </div>
    <div className="flex-1 text-left">
      <p className="text-md font-medium">{name}</p>
      <p className="text-sm font-medium text-text-secondary">{formatNumber(amount)}</p>
    </div>
    <p className="text-md font-bold">{formatUsd(new BigNumber(price ?? '0').times(amount ?? '0').toString())}</p>
  </>
);
