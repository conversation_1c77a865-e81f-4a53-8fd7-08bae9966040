import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { Button } from '@/components/ui/button';
import Display from '@/components/ui/display';
import type { TokenPosition } from '@/module/assets/types';
import { WithdrawAddressInput } from '@/module/wallet-withdraw/withdraw-address-input';
import { WithdrawAmountInput } from '@/module/wallet-withdraw/withdraw-amount-input';
import { WithdrawTokenInput } from '@/module/wallet-withdraw/withdraw-token-input';

export const WithdrawTokenInputs = ({
  token,
  onTokenClick,
  tooltip,
}: {
  token?: TokenPosition;
  onTokenClick: () => void;
  tooltip?: string;
}) => {
  const form = useFormContext();
  const t = useTranslations('wallets');

  const {
    formState: { isSubmitting },
  } = form;

  const recipientAddress = form.watch('destinationWalletAddress') as string;

  return (
    <>
      <div className="flex flex-col gap-1">
        <Display size="L" weight="bold">
          {t('withdraw')}
        </Display>
      </div>
      <div className="flex flex-col gap-2">
        <WithdrawTokenInput token={token} onClick={onTokenClick} />
        <WithdrawAddressInput disabled={!token} />
        <WithdrawAmountInput
          disabled={!token || !recipientAddress ? !form.watch('amountToTransferInWei') : undefined}
          recipientAddress={recipientAddress}
          token={token}
        />
        <Tooltip open={!!tooltip}>
          <TooltipTrigger asChild>
            <Button className="w-full" disabled={isSubmitting} type="submit">
              {t('withdraw')}
            </Button>
          </TooltipTrigger>
          <TooltipContent>{tooltip}</TooltipContent>
        </Tooltip>
      </div>
    </>
  );
};
