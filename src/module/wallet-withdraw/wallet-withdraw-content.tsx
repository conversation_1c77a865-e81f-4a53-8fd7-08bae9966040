'use client';

import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { useState } from 'react';

import { Form } from '@/components/form/form';
import { Card } from '@/components/ui/card';
import { ROUTES } from '@/constants/routes';
import { useRouter } from '@/i18n/routing';
import type { Chain } from '@/lib/api';
import { getErrorMessage } from '@/lib/get-error-message';
import type { TokenPosition } from '@/module/assets/types';
import { useWithdrawForm, type WithdrawFormSchema } from '@/module/wallet-withdraw/hooks/use-withdraw-form';
import { WalletPasswordDialog } from '@/module/wallet-withdraw/wallet-password-dialog';
import { WithdrawChooseToken } from '@/module/wallet-withdraw/withdraw-choose-token';
import { WithdrawTokenInputs } from '@/module/wallet-withdraw/withdraw-inputs';
import { WithdrawSuccessDialog } from '@/module/wallet-withdraw/withdraw-success-dialog';

import { useWithdraw } from './use-withdraw';

interface Props {
  walletId: string;
  walletAddress: string;
  chain: Chain;
}

const SHOW_CHOOSE_TOKEN_QUERY = 'showChooseToken';

export const WalletWithdrawContent = ({ walletId, walletAddress, chain }: Props) => {
  const { replace } = useRouter();
  const t = useTranslations();

  const [showChooseToken] = useQueryState(SHOW_CHOOSE_TOKEN_QUERY);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [withdrawalData, setWithdrawalData] = useState<WithdrawFormSchema | null>(null);
  const [txHash, setTxHash] = useState<string>();

  const [selectedToken, setSelectedToken] = useState<TokenPosition>();

  const { form } = useWithdrawForm(walletAddress, selectedToken);

  const { handleWithdraw } = useWithdraw(walletId);

  const handleSelectToken = (token: TokenPosition) => {
    form.setValue('tokenAddress', token.tokenAddress);
    setSelectedToken(token);
    replace(ROUTES.WALLETS.WITHDRAW(walletId, '', ROUTES.WALLETS.DETAIL(walletId)));
  };

  const onTokenClick = () => {
    replace(
      ROUTES.WALLETS.WITHDRAW(walletId, 'true', ROUTES.WALLETS.WITHDRAW(walletId, '', ROUTES.WALLETS.DETAIL(walletId))),
    );
  };

  const handleFormSubmit = (data: WithdrawFormSchema) => {
    setWithdrawalData(data);
    setShowPasswordDialog(true);
  };

  const handlePasswordSubmitted = async (password: string) => {
    if (!withdrawalData) {
      return;
    }

    try {
      const isNative = !!selectedToken?.isNative;
      const formData = {
        destinationWalletAddress: withdrawalData.destinationWalletAddress,
        toTransferNativeAmount: withdrawalData.amountToTransferInWei,
        tokenAddress: selectedToken?.tokenAddress,
      };

      const result = await handleWithdraw(formData, password, isNative);

      setTxHash(result);
      setShowSuccessDialog(true);
    } catch (error) {
      const errorMessage = getErrorMessage(error);

      // handle other errors in the main form
      if (errorMessage?.includes('insufficient')) {
        form.setError('amountToTransferInWei', {
          message: errorMessage,
        });
      }

      // bubble up to the password dialog
      throw new Error(errorMessage ?? t('common.something-went-wrong'));
    }
  };

  return (
    <Card className="mx-auto max-w-2xl">
      {}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleFormSubmit)}>
          {showChooseToken === 'true' ? (
            <WithdrawChooseToken walletId={walletId} onSetToken={handleSelectToken} />
          ) : (
            <WithdrawTokenInputs token={selectedToken} onTokenClick={onTokenClick} />
          )}
        </form>
        <WithdrawSuccessDialog
          chain={chain}
          open={showSuccessDialog}
          tokenName={selectedToken?.tokenName ?? ''}
          txHash={txHash}
          withdrawAmount={form.watch('amountToTransferInWei')}
          onOpenChange={() => {
            setShowSuccessDialog(!showSuccessDialog);
          }}
        />
        <WalletPasswordDialog
          open={showPasswordDialog}
          onOpenChange={setShowPasswordDialog}
          onPasswordSubmitted={handlePasswordSubmitted}
        />
      </Form>
    </Card>
  );
};
