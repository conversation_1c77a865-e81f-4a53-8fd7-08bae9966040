const isClickable = (element: HTMLElement | null) => {
  if (!element) {
    return false;
  }

  return element.nodeName === 'A' || element.onclick != null || element.getAttribute('role') === 'button';
};

// finds closest clickable parent of an element
export const findClickableTarget = (element: HTMLElement | null): HTMLElement | null => {
  if (!element || element.getAttribute('data-onboarding-clickable') === 'true') {
    return null;
  }

  if (isClickable(element)) {
    return element;
  }

  return findClickableTarget(element.parentElement);
};
