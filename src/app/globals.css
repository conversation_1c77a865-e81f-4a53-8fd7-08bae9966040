@import 'tailwindcss';
@plugin 'tailwindcss-animate';

@custom-variant dark (&:where(.dark, .dark *));

@utility nested-counter {
  &ol {
    counter-reset: section;
  }

  &ol ol {
    counter-reset: item;
  }

  &ol > li {
    counter-increment: section;
  }

  &ol > li ol > li {
    counter-increment: item;
  }

  &ol > li::before {
    @apply mr-1;
    content: counter(section) '.';
  }

  &ol > li > ol > li::before {
    @apply mr-1;
    content: counter(section) '.' counter(item, decimal-leading-zero);
  }

  &ol > li > ol > li > ol > li::before {
    @apply mr-1;
    content: '(' counter(item, lower-alpha) ')';
  }
}

@utility text-balance {
  text-wrap: balance;
}

@utility scrollbar-hidden {
  /* Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

@utility wizard-action-wrapper {
  @apply flex w-full gap-2 py-3 sm:justify-end;
}

@layer utilities {
  html,
  body {
    height: 100%;
  }

  body {
    font-family: Arial, Helvetica, sans-serif;
  }

  /* Disable autofill's opacity in chrome
Our custom error messages looks ugly with opacity enabled */

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus {
    background-color: transparent;
    transition: background-color 200s ease-in-out 0s;
  }

  #storybook-root {
    height: 100%;
  }
}

:root {
  button,
  a {
    cursor: pointer;
  }

  /* PRIMITIVES */
  --Primitives-brands-border-active-darkmode: var(--Primitives-color-primary-default);
  --Primitives-brands-border-active-lightmode: var(--Primitives-color-grey-scale-black);
  --Primitives-brands-border-active-secondary-lightmode: var(--Tokens-color-border-secondary);
  --Primitives-brands-button-border: var(--Primitives-color-grey-scale-black);
  --Primitives-brands-button-surface: var(--Primitives-color-primary-default);
  --Primitives-brands-button-text: var(--Primitives-color-grey-scale-black);
  --Primitives-brands-text-active-darkmode: var(--Primitives-color-primary-default);
  --Primitives-brands-text-active-lightmode: var(--Primitives-color-grey-scale-black);
  --Primitives-brands-text-active-secondary-darkmode: var(--Primitives-color-primary-default);
  --Primitives-brands-text-tag-darkmode: var(--Primitives-color-grey-scale-black);
  --Primitives-brands-text-tag-lightmode: var(--Primitives-color-grey-scale-white);
  --Primitives-color-alpha-black-5: 0, 0%, 0%, 0.05;
  --Primitives-color-alpha-black-10: 0, 0%, 0%, 0.1;
  --Primitives-color-alpha-black-20: 0, 0%, 0%, 0.2;
  --Primitives-color-alpha-black-30: 0, 0%, 0%, 0.3;
  --Primitives-color-alpha-black-40: 0, 0%, 0%, 0.4;
  --Primitives-color-alpha-black-50: 0, 0%, 0%, 0.5;
  --Primitives-color-alpha-black-60: 0, 0%, 0%, 0.6;
  --Primitives-color-alpha-black-70: 0, 0%, 0%, 0.7;
  --Primitives-color-alpha-black-80: 0, 0%, 0%, 0.8;
  --Primitives-color-alpha-black-90: 0, 0%, 0%, 0.9;
  --Primitives-color-alpha-black-95: 0, 0%, 0%, 0.95;
  --Primitives-color-alpha-black-transparent: 0, 0%, 0%, 0;
  --Primitives-color-alpha-white-5: 0, 0%, 100%, 0.05;
  --Primitives-color-alpha-white-10: 0, 0%, 100%, 0.1;
  --Primitives-color-alpha-white-20: 0, 0%, 100%, 0.2;
  --Primitives-color-alpha-white-30: 0, 0%, 100%, 0.3;
  --Primitives-color-alpha-white-40: 0, 0%, 100%, 0.4;
  --Primitives-color-alpha-white-50: 0, 0%, 100%, 0.5;
  --Primitives-color-alpha-white-60: 0, 0%, 100%, 0.6;
  --Primitives-color-alpha-white-70: 0, 0%, 100%, 0.7;
  --Primitives-color-alpha-white-80: 0, 0%, 100%, 0.8;
  --Primitives-color-alpha-white-90: 0, 0%, 100%, 0.9;
  --Primitives-color-alpha-white-95: 0, 0%, 100%, 0.95;
  --Primitives-color-alpha-white-transparent: 0, 0%, 100%, 0;
  --Primitives-color-event-error-error: 4 100% 68%;
  --Primitives-color-event-error-error-darker: 4 86% 45%;
  --Primitives-color-event-error-error-deep: 4, 86%, 58%, 0.2;
  --Primitives-color-event-error-error-lighter: 4 86% 75%;
  --Primitives-color-event-error-error-subtle: 5 85% 95%;
  --Primitives-color-event-error-error-card: 348 37% 20%;
  --Primitives-color-event-success-success: 152 77% 39%;
  --Primitives-color-event-success-success-darker: 152 77% 26%;
  --Primitives-color-event-success-success-deep: 152, 76%, 39%, 0.2;
  --Primitives-color-event-success-success-lighter: 152 77% 50%;
  --Primitives-color-event-success-success-subtle: 152 77% 95%;
  --Primitives-color-event-success-success-card: 165 36% 16%;
  --Primitives-color-event-warning-warning: 34 94% 50%;
  --Primitives-color-event-warning-warning-darker: 34 94% 40%;
  --Primitives-color-event-warning-warning-deep: 34, 94%, 50%, 0.2;
  --Primitives-color-event-warning-warning-lighter: 34 93% 70%;
  --Primitives-color-event-warning-warning-subtle: 35 92% 95%;
  --Primitives-color-event-warning-warning-card: 27 44% 20%;
  --Primitives-color-grey-scale-10: 0 0% 10%;
  --Primitives-color-grey-scale-20: 0 0% 20%;
  --Primitives-color-grey-scale-30: 0 0% 30%;
  --Primitives-color-grey-scale-40: 0 0% 40%;
  --Primitives-color-grey-scale-50: 0 0% 50%;
  --Primitives-color-grey-scale-60: 0 0% 60%;
  --Primitives-color-grey-scale-70: 0 0% 70%;
  --Primitives-color-grey-scale-80: 0 0% 80%;
  --Primitives-color-grey-scale-90: 0 0% 90%;
  --Primitives-color-grey-scale-black: 0 0% 0%;
  --Primitives-color-grey-scale-white: 0 0% 100%;
  --Primitives-color-primary-accent: 54 100% 50%;
  --Primitives-color-primary-card: 7 10% 18%;
  --Primitives-color-primary-alpha-dark: 46, 100%, 69%, 0.7;
  --Primitives-color-primary-alpha-light: 46, 100%, 69%, 0.2;
  --Primitives-color-primary-alpha-subtle: 46, 100%, 69%, 0.1;
  --Primitives-color-primary-darker: 46 100% 40%;
  --Primitives-color-primary-deep: 46 100% 10%;
  --Primitives-color-primary-default: 46 100% 69%;
  --Primitives-color-primary-lighter: 46 100% 80%;
  --Primitives-color-primary-subtle: 46 100% 90%;
  --Primitives-color-secondary-area: 260 20% 12%;
  --Primitives-color-secondary-chain-tag: 250 11% 31%;
  --Primitives-color-secondary-darker: 256 100% 56%;
  --Primitives-color-secondary-deep: 258 25% 10%;
  --Primitives-color-secondary-default: 256 100% 66%;
  --Primitives-color-secondary-elevated: 255 14% 16%;
  --Primitives-color-secondary-front: 254 8% 57%;
  --Primitives-color-secondary-lighter: 256 100% 76%;
  --Primitives-color-secondary-subtle: 255 100% 90%;
  --Primitives-color-secondary-third-layer: 256 10% 21%;
  --Primitives-color-purple-beta-background: 256 29% 27%;
  --Primitives-color-purple-beta-content: 256 100% 76%;
  --Primitives-color-discord: 235 86% 65%;
  /*TOKENS*/
  --Tokens-color-alpha-5: var(--Primitives-color-alpha-white-5);
  --Tokens-color-alpha-10: var(--Primitives-color-alpha-white-10);
  --Tokens-color-alpha-20: var(--Primitives-color-alpha-white-20);
  --Tokens-color-alpha-30: var(--Primitives-color-alpha-white-30);
  --Tokens-color-alpha-40: var(--Primitives-color-alpha-white-40);
  --Tokens-color-alpha-50: var(--Primitives-color-alpha-white-50);
  --Tokens-color-alpha-60: var(--Primitives-color-alpha-white-60);
  --Tokens-color-alpha-70: var(--Primitives-color-alpha-white-70);
  --Tokens-color-alpha-80: var(--Primitives-color-alpha-white-80);
  --Tokens-color-alpha-90: var(--Primitives-color-alpha-white-90);
  --Tokens-color-alpha-95: var(--Primitives-color-alpha-white-95);
  --Tokens-color-alpha-inverted-5: var(--Primitives-color-alpha-black-5);
  --Tokens-color-alpha-inverted-10: var(--Primitives-color-alpha-black-10);
  --Tokens-color-alpha-inverted-20: var(--Primitives-color-alpha-black-20);
  --Tokens-color-alpha-inverted-30: var(--Primitives-color-alpha-black-30);
  --Tokens-color-alpha-inverted-40: var(--Primitives-color-alpha-black-40);
  --Tokens-color-alpha-inverted-50: var(--Primitives-color-alpha-black-50);
  --Tokens-color-alpha-inverted-60: var(--Primitives-color-alpha-black-60);
  --Tokens-color-alpha-inverted-70: var(--Primitives-color-alpha-black-70);
  --Tokens-color-alpha-inverted-80: var(--Primitives-color-alpha-black-80);
  --Tokens-color-alpha-inverted-90: var(--Primitives-color-alpha-black-90);
  --Tokens-color-alpha-inverted-95: var(--Primitives-color-alpha-black-95);
  --Tokens-color-border-BG-color-mask: var(--Primitives-color-grey-scale-90);
  --Tokens-color-border-active-secondary: var(--Primitives-brands-border-active-secondary-lightmode);
  --Tokens-color-border-active: var(--Primitives-brands-border-active-lightmode);
  --Tokens-color-border-brand-1-hover: var(--Primitives-color-primary-darker);
  --Tokens-color-border-brand-1: var(--Primitives-color-primary-default);
  --Tokens-color-border-brand-2: var(--Primitives-color-secondary-default);
  --Tokens-color-border-button: var(--Primitives-brands-button-border);
  --Tokens-color-border-invert: var(--Primitives-color-grey-scale-white);
  --Tokens-color-border-primary: var(--Primitives-color-grey-scale-black);
  --Tokens-color-border-secondary: var(--Primitives-color-grey-scale-40);
  --Tokens-color-border-segcontrol-bg: var(--Primitives-color-grey-scale-30);
  --Tokens-color-border-segcontrol-front: var(--Primitives-color-grey-scale-black);
  --Tokens-color-border-subtle: var(--Primitives-color-grey-scale-80);
  --Tokens-color-event-error-solidbg: var(--Primitives-color-event-error-error-subtle);
  --Tokens-color-event-error-background: var(--Primitives-color-event-error-error-subtle);
  --Tokens-color-event-error-content: var(--Primitives-color-event-error-error-darker);
  --Tokens-color-event-error-default: var(--Primitives-color-event-error-error);
  --Tokens-color-event-success-solidbg: var(--Primitives-color-event-success-success-subtle);
  --Tokens-color-event-success-background: var(--Primitives-color-event-success-success-subtle);
  --Tokens-color-event-success-content: var(--Primitives-color-event-success-success-darker);
  --Tokens-color-event-success-default: var(--Primitives-color-event-success-success);
  --Tokens-color-event-warning-solidbg: var(--Primitives-color-event-warning-warning-subtle);
  --Tokens-color-event-warning-background: var(--Primitives-color-event-warning-warning-subtle);
  --Tokens-color-event-warning-content: var(--Primitives-color-event-warning-warning-darker);
  --Tokens-color-event-warning-default: var(--Primitives-color-event-warning-warning);
  --Tokens-color-surface-active: var(--Primitives-color-primary-default);
  --Tokens-color-surface-area: var(--Primitives-color-secondary-elevated);
  --Tokens-color-surface-chain-tag: var(--Primitives-color-secondary-elevated);
  --Tokens-color-surface-card-bg: var(--Primitives-color-secondary-elevated);
  --Tokens-color-surface-background: var(--Primitives-color-grey-scale-90);
  --Tokens-color-surface-brand-1-alpha-cards: var(--Primitives-color-primary-alpha-subtle);
  --Tokens-color-surface-brand-1-alpha-chips: var(--Primitives-color-primary-alpha-dark);
  --Tokens-color-surface-brand-1-button: var(--Primitives-color-primary-default);
  --Tokens-color-surface-brand-1-changable: var(--Primitives-color-primary-default);
  --Tokens-color-surface-brand-1-hover: var(--Primitives-color-primary-darker);
  --Tokens-color-surface-brand-1: var(--Primitives-color-primary-default);
  --Tokens-color-surface-brand-2: var(--Primitives-color-secondary-default);
  --Tokens-color-surface-button: var(--Primitives-brands-button-surface);
  --Tokens-color-surface-elevated: var(--Primitives-color-secondary-elevated);
  --Tokens-color-surface-grey: var(--Primitives-color-grey-scale-40);
  --Tokens-color-surface-invert: var(--Primitives-color-grey-scale-black);
  --Tokens-color-surface-primary: var(--Primitives-color-grey-scale-white);
  --Tokens-color-surface-secondary-elevated: var(--Primitives-color-grey-scale-90);
  --Tokens-color-surface-secondary: var(--Primitives-color-grey-scale-90);
  --Tokens-color-surface-segcontrol-bg: var(--Primitives-color-grey-scale-80);
  --Tokens-color-surface-segcontrol-front: var(--Primitives-color-grey-scale-white);
  --Tokens-color-surface-segment-control: var(--Primitives-color-grey-scale-white);
  --Tokens-color-surface-subtle: var(--Primitives-color-grey-scale-90);
  --Tokens-color-surface-third-layer: var(--Primitives-color-secondary-elevated);
  --Tokens-color-surface-toggle: var(--Primitives-color-grey-scale-white);
  --Tokens-color-text-active-secondary: var(--Primitives-brands-border-active-secondary-lightmode);
  --Tokens-color-text-active: var(--Primitives-brands-text-active-lightmode);
  --Tokens-color-text-brand-1-hover: var(--Primitives-color-primary-darker);
  --Tokens-color-text-brand-1: var(--Primitives-color-primary-default);
  --Tokens-color-text-brand-2: var(--Primitives-color-secondary-default);
  --Tokens-color-text-button: var(--Primitives-brands-button-text);
  --Tokens-color-text-elevated: var(--Primitives-color-grey-scale-40);
  --Tokens-color-text-invert: var(--Primitives-color-grey-scale-white);
  --Tokens-color-text-primary: var(--Primitives-color-grey-scale-black);
  --Tokens-color-text-secondary: var(--Primitives-color-grey-scale-40);
  --Tokens-color-text-segcontrol-bg: var(--Primitives-color-grey-scale-30);
  --Tokens-color-text-segcontrol-front: var(--Primitives-color-grey-scale-black);
  --Tokens-color-text-subtle: var(--Primitives-color-grey-scale-80);
  --Tokens-color-text-tag: var(--Primitives-brands-text-tag-lightmode);
  /* TYPOGRAPHY */
  --font-family-display-fat-bot: 'Goldplay';
  --font-family-display-mode: 'Goldplay';
  --font-family-text-fat-bot: 'Goldplay';
  --font-family-text-mode: 'Goldplay';
  --font-weight-regular-fat-bot: 400;
  --font-weight-regular-mode: 400;
  --font-weight-medium-fat-bot: 500;
  --font-weight-medium-mode: 500;
  --font-weight-semibold-fat-bot: 600;
  --font-weight-semibold-mode: 600;
  --font-weight-bold-fat-bot: 700;
  --font-weight-bold-mode: 700;
  --font-size-display-xxxl-fat-bot: 150px;
  --font-size-display-xxxl-rem-fat-bot: 9.375rem;
  --font-size-display-xxxl-mode: 150px;
  --font-size-display-xxxl-rem-mode: 9.375rem;
  --font-size-display-xxl-fat-bot: 72px;
  --font-size-display-xxl-rem-fat-bot: 4.5rem;
  --font-size-display-xxl-mode: 72px;
  --font-size-display-xxl-rem-mode: 4.5rem;
  --font-size-display-xl-fat-bot: 60px;
  --font-size-display-xl-rem-fat-bot: 3.75rem;
  --font-size-display-xl-mode: 60px;
  --font-size-display-xl-rem-mode: 3.75rem;
  --font-size-display-l-fat-bot: 48px;
  --font-size-display-l-rem-fat-bot: 3rem;
  --font-size-display-l-mode: 48px;
  --font-size-display-l-rem-mode: 3rem;
  --font-size-display-m-fat-bot: 36px;
  --font-size-display-m-rem-fat-bot: 2.25rem;
  --font-size-display-m-mode: 36px;
  --font-size-display-m-rem-mode: 2.25rem;
  --font-size-display-s-fat-bot: 30px;
  --font-size-display-s-rem-fat-bot: 1.875rem;
  --font-size-display-s-mode: 30px;
  --font-size-display-s-rem-mode: 1.875rem;
  --font-size-display-xs-fat-bot: 24px;
  --font-size-display-xs-rem-fat-bot: 1.5rem;
  --font-size-display-xs-mode: 24px;
  --font-size-display-xs-rem-mode: 1.5rem;
  --font-size-text-xl-fat-bot: 20px;
  --font-size-text-xl-rem-fat-bot: 1.25rem;
  --font-size-text-xl-mode: 20px;
  --font-size-text-xl-rem-mode: 1.25rem;
  --font-size-text-l-fat-bot: 18px;
  --font-size-text-l-rem-fat-bot: 1.125rem;
  --font-size-text-l-mode: 18px;
  --font-size-text-l-rem-mode: 1.125rem;
  --font-size-text-m-fat-bot: 16px;
  --font-size-text-m-rem-fat-bot: 1rem;
  --font-size-text-m-mode: 16px;
  --font-size-text-m-rem-mode: 1rem;
  --font-size-text-s-fat-bot: 14px;
  --font-size-text-s-rem-fat-bot: 0.875rem;
  --font-size-text-s-mode: 14px;
  --font-size-text-s-rem-mode: 0.875rem;
  --font-size-text-xs-fat-bot: 12px;
  --font-size-text-xs-rem-fat-bot: 0.75rem;
  --font-size-text-xs-mode: 12px;
  --font-size-text-xs-rem-mode: 0.75rem;
  --font-size-text-xxs-fat-bot: 10px;
  --font-size-text-xxs-rem-fat-bot: 0.625rem;
  --font-size-text-xxs-mode: 10px;
  --font-size-text-xxs-rem-mode: 0.625rem;
  --font-line-height-150-fat-bot: 150px;
  --font-line-height-150-rem-fat-bot: 9.375rem;
  --font-line-height-150-mode: 150px;
  --font-line-height-150-rem-mode: 9.375rem;
  --font-line-height-72-fat-bot: 94px;
  --font-line-height-72-rem-fat-bot: 5.875rem;
  --font-line-height-72-mode: 94px;
  --font-line-height-72-rem-mode: 5.875rem;
  --font-line-height-60-fat-bot: 78px;
  --font-line-height-60-rem-fat-bot: 4.875rem;
  --font-line-height-60-mode: 78px;
  --font-line-height-60-rem-mode: 4.875rem;
  --font-line-height-48-fat-bot: 62px;
  --font-line-height-48-rem-fat-bot: 3.875rem;
  --font-line-height-48-mode: 62px;
  --font-line-height-48-rem-mode: 3.875rem;
  --font-line-height-36-fat-bot: 46px;
  --font-line-height-36-rem-fat-bot: 2.875rem;
  --font-line-height-36-mode: 46px;
  --font-line-height-36-rem-mode: 2.875rem;
  --font-line-height-30-fat-bot: 39px;
  --font-line-height-30-rem-fat-bot: 2.4375rem;
  --font-line-height-30-mode: 39px;
  --font-line-height-30-rem-mode: 2.4375rem;
  --font-line-height-24-fat-bot: 31px;
  --font-line-height-24-rem-fat-bot: 1.9375rem;
  --font-line-height-24-mode: 31px;
  --font-line-height-24-rem-mode: 1.9375rem;
  --font-line-height-20-fat-bot: 26px;
  --font-line-height-20-rem-fat-bot: 1.625rem;
  --font-line-height-20-mode: 26px;
  --font-line-height-20-rem-mode: 1.625rem;
  --font-line-height-18-fat-bot: 23px;
  --font-line-height-18-rem-fat-bot: 1.4375rem;
  --font-line-height-18-mode: 23px;
  --font-line-height-18-rem-mode: 1.4375rem;
  --font-line-height-16-fat-bot: 21px;
  --font-line-height-16-rem-fat-bot: 1.3125rem;
  --font-line-height-16-mode: 21px;
  --font-line-height-16-rem-mode: 1.3125rem;
  --font-line-height-14-fat-bot: 18px;
  --font-line-height-14-rem-fat-bot: 1.125rem;
  --font-line-height-14-mode: 18px;
  --font-line-height-14-rem-mode: 1.125rem;
  --font-line-height-12-fat-bot: 16px;
  --font-line-height-12-rem-fat-bot: 1rem;
  --font-line-height-12-mode: 16px;
  --font-line-height-12-rem-mode: 1rem;
  --font-line-height-10-fat-bot: 13px;
  --font-line-height-10-rem-fat-bot: 0.8125rem;
  --font-line-height-10-mode: 13px;
  --font-line-height-10-rem-mode: 0.8125rem;
  /*BOX-SHADOW*/
  --shadow-primary: 0px 4px 0px 0px hsla(0, 0%, 0%, 0.25);
  --shadow-primary-inner: 0px 4px 0px 0px hsla(0, 0%, 0%, 0.25) inset;
  --shadow-success: 0px 4px 0px 0px hsla(152, 77%, 30%, 1);
  --shadow-error: 0px 4px 0px 0px hsla(4, 60%, 48%, 1);
  --shadow-info: 0px 4px 0px 0px hsla(34, 83%, 50%, 1);
  --shadow-surface-brand: 0px 4px 0px 0px var(--color-primary-darker);
}

:root[class~='dark'] {
  --Tokens-color-alpha-5: var(--Primitives-color-alpha-black-5);
  --Tokens-color-alpha-10: var(--Primitives-color-alpha-black-10);
  --Tokens-color-alpha-20: var(--Primitives-color-alpha-black-20);
  --Tokens-color-alpha-30: var(--Primitives-color-alpha-black-30);
  --Tokens-color-alpha-40: var(--Primitives-color-alpha-black-40);
  --Tokens-color-alpha-50: var(--Primitives-color-alpha-black-50);
  --Tokens-color-alpha-60: var(--Primitives-color-alpha-black-60);
  --Tokens-color-alpha-70: var(--Primitives-color-alpha-black-70);
  --Tokens-color-alpha-80: var(--Primitives-color-alpha-black-80);
  --Tokens-color-alpha-90: var(--Primitives-color-alpha-black-90);
  --Tokens-color-alpha-95: var(--Primitives-color-alpha-black-95);
  --Tokens-color-alpha-inverted-5: var(--Primitives-color-alpha-white-5);
  --Tokens-color-alpha-inverted-10: var(--Primitives-color-alpha-white-10);
  --Tokens-color-alpha-inverted-20: var(--Primitives-color-alpha-white-20);
  --Tokens-color-alpha-inverted-30: var(--Primitives-color-alpha-white-30);
  --Tokens-color-alpha-inverted-40: var(--Primitives-color-alpha-white-40);
  --Tokens-color-alpha-inverted-50: var(--Primitives-color-alpha-white-50);
  --Tokens-color-alpha-inverted-60: var(--Primitives-color-alpha-white-60);
  --Tokens-color-alpha-inverted-70: var(--Primitives-color-alpha-white-70);
  --Tokens-color-alpha-inverted-80: var(--Primitives-color-alpha-white-80);
  --Tokens-color-alpha-inverted-90: var(--Primitives-color-alpha-white-90);
  --Tokens-color-alpha-inverted-95: var(--Primitives-color-alpha-white-95);
  /*BORDER*/
  --Tokens-color-border-BG-color-mask: var(--Primitives-color-grey-scale-10);
  --Tokens-color-border-active-secondary: var(--Primitives-brands-text-active-secondary-darkmode);
  --Tokens-color-border-active: var(--Primitives-brands-border-active-darkmode);
  --Tokens-color-border-brand-1-hover: var(--Primitives-color-primary-default);
  --Tokens-color-border-brand-1: var(--Primitives-color-primary-default);
  --Tokens-color-border-brand-2: var(--Primitives-color-secondary-default);
  --Tokens-color-border-button: var(--Primitives-brands-button-border);
  --Tokens-color-border-invert: var(--Primitives-color-grey-scale-black);
  --Tokens-color-border-primary: var(--Primitives-color-grey-scale-white);
  --Tokens-color-border-secondary: var(--Primitives-color-secondary-front);
  --Tokens-color-border-segcontrol-bg: var(--Primitives-color-grey-scale-70);
  --Tokens-color-border-segcontrol-front: var(--Primitives-color-grey-scale-white);
  --Tokens-color-border-subtle: var(--Primitives-color-secondary-third-layer);
  /*EVENT*/
  --Tokens-color-event-error-solidbg: var(--Primitives-color-event-error-error-card);
  --Tokens-color-event-error-background: var(--Primitives-color-event-error-error-deep);
  --Tokens-color-event-error-content: var(--Primitives-color-event-error-error);
  --Tokens-color-event-error-default: var(--Primitives-color-event-error-error);
  --Tokens-color-event-success-solidbg: var(--Primitives-color-event-success-success-card);
  --Tokens-color-event-success-background: var(--Primitives-color-event-success-success-deep);
  --Tokens-color-event-success-background-subtle: var(--Primitives-color-event-success-success-subtle);
  --Tokens-color-event-success-content: var(--Primitives-color-event-success-success-lighter);
  --Tokens-color-event-success-default: var(--Primitives-color-event-success-success);
  --Tokens-color-event-warning-solidbg: var(--Primitives-color-event-warning-warning-card);
  --Tokens-color-event-warning-background: var(--Primitives-color-event-warning-warning-deep);
  --Tokens-color-event-warning-content: var(--Primitives-color-event-warning-warning-lighter);
  --Tokens-color-event-warning-default: var(--Primitives-color-event-warning-warning);
  /*SURFACE*/
  --Tokens-color-surface-active: var(--Primitives-color-primary-default);
  --Tokens-color-surface-area: var(--Primitives-color-secondary-area);
  --Tokens-color-surface-chain-tag: var(--Primitives-color-secondary-chain-tag);
  --Tokens-color-surface-card-bg: var(--Primitives-color-primary-card);
  --Tokens-color-surface-background: var(--Primitives-color-secondary-deep);
  --Tokens-color-surface-brand-1-alpha-cards: var(--Primitives-color-primary-alpha-subtle);
  --Tokens-color-surface-brand-1-alpha-chips: var(--Primitives-color-primary-alpha-dark);
  --Tokens-color-surface-brand-1-button: var(--Primitives-color-primary-default);
  --Tokens-color-surface-brand-1-changable: var(--Primitives-color-primary-lighter);
  --Tokens-color-surface-brand-1-hover: var(--Primitives-color-primary-lighter);
  --Tokens-color-surface-brand-1: var(--Primitives-color-primary-default);
  --Tokens-color-surface-brand-2: var(--Primitives-color-secondary-default);
  --Tokens-color-surface-button: var(--Primitives-brands-button-surface);
  --Tokens-color-surface-elevated: var(--Primitives-color-secondary-elevated);
  --Tokens-color-surface-grey: var(--Primitives-color-secondary-front);
  --Tokens-color-surface-invert: var(--Primitives-color-grey-scale-white);
  --Tokens-color-surface-primary: var(--Primitives-color-secondary-elevated);
  --Tokens-color-surface-secondary-elevated: var(--Primitives-color-grey-scale-30);
  --Tokens-color-surface-secondary: var(--Primitives-color-grey-scale-20);
  --Tokens-color-surface-segcontrol-bg: var(--Primitives-color-grey-scale-20);
  --Tokens-color-surface-segcontrol-front: var(--Primitives-color-grey-scale-30);
  --Tokens-color-surface-segment-control: var(--Primitives-color-grey-scale-40);
  --Tokens-color-surface-subtle: var(--Primitives-color-grey-scale-20);
  --Tokens-color-surface-third-layer: var(--Primitives-color-secondary-third-layer);
  --Tokens-color-surface-toggle: var(--Primitives-color-grey-scale-white);
  /*TEXT*/
  --Tokens-color-text-active-secondary: var(--Primitives-brands-text-active-secondary-darkmode);
  --Tokens-color-text-active: var(--Primitives-brands-text-active-darkmode);
  --Tokens-color-text-brand-1-hover: var(--Primitives-color-primary-default);
  --Tokens-color-text-brand-1: var(--Primitives-color-primary-default);
  --Tokens-color-text-brand-2: var(--Primitives-color-secondary-default);
  --Tokens-color-text-button: var(--Primitives-brands-button-text);
  --Tokens-color-text-elevated: var(--Primitives-color-secondary-front);
  --Tokens-color-text-invert: var(--Primitives-color-grey-scale-black);
  --Tokens-color-text-primary: var(--Primitives-color-grey-scale-white);
  --Tokens-color-text-secondary: var(--Primitives-color-secondary-front);
  --Tokens-color-text-segcontrol-bg: var(--Primitives-color-secondary-front);
  --Tokens-color-text-segcontrol-front: var(--Primitives-color-grey-scale-white);
  --Tokens-color-text-subtle: var(--Primitives-color-secondary-front);
  --Tokens-color-text-tag: var(--Primitives-brands-text-tag-darkmode);
}

@theme {
  /* BREAKPOINTS */
  --breakpoint-3xl: 120rem; /* 1920px */
  --breakpoint-4xl: 160rem; /* 2560px */
  /* COLORS */
  --color-black: hsl(var(--Primitives-color-grey-scale-black));
  --color-white: hsl(var(--Primitives-color-grey-scale-white));
  --color-area: hsl(var(--Tokens-color-surface-area));
  --color-elevated: hsl(var(--Tokens-color-surface-elevated));
  --color-thirdLayer: hsl(var(--Tokens-color-surface-third-layer));
  /* TEXT COLOR */
  --color-text-primary: hsl(var(--Tokens-color-text-primary));
  --color-text-secondary: hsl(var(--Tokens-color-text-secondary));
  --color-text-elevated: hsl(var(--Tokens-color-text-elevated));
  --color-text-subtle: hsl(var(--Tokens-color-text-subtle));
  --color-text-invert: hsl(var(--Tokens-color-text-invert));
  --color-text-brand-1: hsl(var(--Tokens-color-text-brand-1));
  --color-text-brand-1-hover: hsl(var(--Tokens-color-text-brand-1-hover));
  --color-text-brand-2: hsl(var(--Tokens-color-text-brand-2));
  --color-text-button: hsl(var(--Tokens-color-text-button));
  --color-text-active: hsl(var(--Tokens-color-text-active));
  --color-text-active-secondary: hsl(var(--Tokens-color-text-active-secondary));
  --color-text-tag: hsl(var(--Tokens-color-text-tag));
  --color-text-segcontrol-front: hsl(var(--Tokens-color-text-segcontrol-front));
  --color-text-segcontrol-bg: hsl(var(--Tokens-color-text-segcontrol-bg));
  /* SURFACE COLOR */
  --color-surface-primary: hsl(var(--Tokens-color-surface-primary));
  --color-surface-background: hsl(var(--Tokens-color-surface-background));
  --color-surface-segment-control: hsl(var(--Tokens-color-surface-segment-control));
  --color-surface-secondary: hsl(var(--Tokens-color-surface-secondary));
  --color-surface-subtle: hsl(var(--Tokens-color-surface-subtle));
  --color-surface-secondary-elevated: hsl(var(--Tokens-color-surface-secondary-elevated));
  --color-surface-invert: hsl(var(--Tokens-color-surface-invert));
  --color-surface-grey: hsl(var(--Tokens-color-surface-grey));
  --color-surface-brand-1: hsl(var(--Tokens-color-surface-brand-1));
  --color-surface-active: hsl(var(--Tokens-color-surface-active));
  --color-surface-brand-1-button: hsl(var(--Tokens-color-surface-brand-1-button));
  --color-surface-brand-1-changable: hsl(var(--Tokens-color-surface-brand-1-changable));
  --color-surface-brand-1-hover: hsl(var(--Tokens-color-surface-brand-1-hover));
  --color-surface-brand-1-alpha-chips: hsl(var(--Tokens-color-surface-brand-1-alpha-chips));
  --color-surface-brand-1-alpha-cards: hsl(var(--Tokens-color-surface-brand-1-alpha-cards));
  --color-surface-brand-2: hsl(var(--Tokens-color-surface-brand-2));
  --color-surface-button: hsl(var(--Tokens-color-surface-button));
  --color-surface-segcontrol-front: hsl(var(--Tokens-color-surface-segcontrol-front));
  --color-surface-segcontrol-bg: hsl(var(--Tokens-color-surface-segcontrol-bg));
  --color-surface-toggle: hsl(var(--Tokens-color-surface-toggle));
  --color-surface-elevated: hsl(var(--Tokens-color-surface-elevated));
  --color-surface-third-layer: hsl(var(--Tokens-color-surface-third-layer));
  --color-surface-area: hsl(var(--Tokens-color-surface-area));
  --color-surface-chain-tag: hsl(var(--Tokens-color-surface-chain-tag));
  --color-surface-card-bg: hsl(var(--Tokens-color-surface-card-bg));
  /* PRIMARY COLOR */
  --color-primary: hsl(var(--Primitives-color-primary-default));
  --color-primary-lighter: hsl(var(--Primitives-color-primary-lighter));
  --color-primary-subtle: hsl(var(--Primitives-color-primary-subtle));
  --color-primary-darker: hsl(var(--Primitives-color-primary-darker));
  --color-primary-deep: hsl(var(--Primitives-color-primary-deep));
  --color-primary-accent: hsl(var(--Primitives-color-primary-accent));
  --color-primary-alphaDark: hsla(var(--Primitives-color-primary-alpha-dark));
  --color-primary-alphaLight: hsla(var(--Primitives-color-primary-alpha-light));
  --color-primary-alphaSubtle: hsla(var(--Primitives-color-primary-alpha-subtle));
  /* SECONDARY COLOR */
  --color-secondary: hsl(var(--Primitives-color-secondary-default));
  --color-secondary-lighter: hsl(var(--Primitives-color-secondary-lighter));
  --color-secondary-subtle: hsl(var(--Primitives-color-secondary-subtle));
  --color-secondary-darker: hsl(var(--Primitives-color-secondary-darker));
  --color-secondary-deep: hsl(var(--Primitives-color-secondary-deep));
  --color-secondary-accent: hsl(var(--Primitives-color-primary-accent));
  --color-secondary-elevated: hsl(var(--Primitives-color-secondary-elevated));
  --color-secondary-area: hsl(var(--Primitives-color-secondary-area));
  --color-secondary-front: hsl(var(--Primitives-color-secondary-front));
  --color-secondary-thirdLayer: hsl(var(--Primitives-color-secondary-third-layer));
  /* EVENT SUCCESS COLOR */
  --color-event-success: hsl(var(--Tokens-color-event-success-default));
  --color-event-success-content: hsl(var(--Tokens-color-event-success-content));
  --color-event-success-background: hsl(var(--Tokens-color-event-success-background));
  --color-event-success-background-subtle: hsl(var(--Tokens-color-event-success-background-subtle));
  --color-event-success-solidbg: hsl(var(--Tokens-color-event-success-solidbg));
  /* EVENT WARNING */
  --color-event-warning: hsl(var(--Tokens-color-event-warning-default));
  --color-event-warning-content: hsl(var(--Tokens-color-event-warning-content));
  --color-event-warning-background: hsl(var(--Tokens-color-event-warning-background));
  --color-event-warning-solidbg: hsl(var(--Tokens-color-event-warning-solidbg));
  /* EVENT ERROR  */
  --color-event-error: hsl(var(--Tokens-color-event-error-default));
  --color-event-error-content: hsl(var(--Tokens-color-event-error-content));
  --color-event-error-background: hsl(var(--Tokens-color-event-error-background));
  --color-event-error-solidbg: hsl(var(--Tokens-color-event-error-solidbg));
  /* BORDER COLOR */
  --color-border-primary: hsl(var(--Tokens-color-border-primary));
  --color-border-secondary: hsl(var(--Tokens-color-border-secondary));
  --color-border-subtle: hsl(var(--Tokens-color-border-subtle));
  --color-border-invert: hsl(var(--Tokens-color-border-invert));
  --color-border-brand-1: hsl(var(--Tokens-color-border-brand-1));
  --color-border-brand-1-hover: hsl(var(--Tokens-color-border-brand-1-hover));
  --color-border-brand-2: hsl(var(--Tokens-color-border-brand-2));
  --color-border-button: hsl(var(--Tokens-color-border-button));
  --color-border-active: hsl(var(--Tokens-color-border-active));
  --color-border-active-secondary: hsl(var(--Tokens-color-border-active-secondary));
  --color-border-segcontrol-bg: hsl(var(--Tokens-color-border-segcontrol-bg));
  --color-border-BG-color-mask: hsl(var(--Tokens-color-border-BG-color-mask));
  --color-border-deep: hsl(var(--Primitives-color-secondary-deep));
  --color-discord: hsl(var(--Primitives-color-discord));
  /* PURPLE COLOR */
  --color-purple-beta-background: hsl(var(--Primitives-color-purple-beta-background));
  --color-purple-beta-content: hsl(var(--Primitives-color-purple-beta-content));
  /* RADIUS */
  --radius-sharp: 0px;
  --radius-xxs: 4px;
  --radius-xs: 8px;
  --radius-sm: 12px;
  --radius-md: 16px;
  --radius-lg: 24px;
  --radius-xl: 32px;
  --radius-xxl: 40px;
  --radius-round: 999px;
  --radius-button: 999px;
  /* SPACING */
  --spacing-no: 0;
  --spacing-0\.25: 2px;
  --spacing-0\.5: 4px;
  --spacing-1: 8px;
  --spacing-1\.5: 12px;
  --spacing-2: 16px;
  --spacing-3: 24px;
  --spacing-4: 32px;
  --spacing-5: 40px;
  --spacing-6: 48px;
  --spacing-7: 56px;
  --spacing-7\.5: 60px;
  --spacing-8: 64px;
  --spacing-9: 72px;
  --spacing-10: 80px;
  --spacing-15: 120px;
  /* LINE HEIGHT */
  --leading-150: var(--font-line-height-150-mode);
  --leading-72: var(--font-line-height-72-mode);
  --leading-60: var(--font-line-height-60-mode);
  --leading-48: var(--font-line-height-48-mode);
  --leading-36: var(--font-line-height-36-mode);
  --leading-30: var(--font-line-height-30-mode);
  --leading-24: var(--font-line-height-24-mode);
  --leading-20: var(--font-line-height-20-mode);
  --leading-18: var(--font-line-height-18-mode);
  --leading-16: var(--font-line-height-16-mode);
  --leading-14: var(--font-line-height-14-mode);
  --leading-12: var(--font-line-height-12-mode);
  --leading-10: var(--font-line-height-10-mode);
  /* FONT WEIGHT*/
  --font-weight-regular: var(--font-weight-regular-mode);
  --font-weight-medium: var(--font-weight-medium-mode);
  --font-weight-semibold: var(--font-weight-semibold-mode);
  --font-weight-bold: var(--font-weight-bold-mode);
  /* FONT */
  --text-display-xxxl: var(--font-size-display-xxxl-mode);
  --text-display-xxl: var(--font-size-display-xxl-mode);
  --text-display-xl: var(--font-size-display-xl-mode);
  --text-display-l: var(--font-size-display-l-mode);
  --text-display-m: var(--font-size-display-m-mode);
  --text-display-s: var(--font-size-display-s-mode);
  --text-display-xs: var(--font-size-display-xs-mode);
  --text-display-xxl--line-height: var(--font-line-height-72-mode);
  --text-display-xl--line-height: var(--font-line-height-60-mode);
  --text-display-l--line-height: var(--font-line-height-48-mode);
  --text-display-m--line-height: var(--font-line-height-36-mode);
  --text-display-s--line-height: var(--font-line-height-30-mode);
  --text-display-xs--line-height: var(--font-line-height-24-mode);
  --text-xl: var(--font-size-text-xl-mode);
  --text-lg: var(--font-size-text-l-mode);
  --text-md: var(--font-size-text-m-mode);
  --text-sm: var(--font-size-text-s-mode);
  --text-xs: var(--font-size-text-xs-mode);
  --text-xxs: var(--font-size-text-xxs-mode);
  --text-xl--line-height: var(--font-line-height-20-mode);
  --text-lg--line-height: var(--font-line-height-18-mode);
  --text-md--line-height: var(--font-line-height-16-mode);
  --text-sm--line-height: var(--font-line-height-14-mode);
  --text-xs--line-height: var(--font-line-height-12-mode);
  --text-xxs--line-height: var(--font-line-height-10-mode);
  /*  SHADOW */
  --shadow-button: 0 4px 0px 0px var(--color-primary-darker);
  --shadow-slider-thumb: 0 2px 0px 0px var(--color-primary-darker);
  --shadow-primary: var(--shadow-primary);
  --shadow-active: var(--shadow-active);
  --shadow-primary-inner: var(--shadow-primary-inner);
  --drop-shadow-sm: filter 0 12px 0px var(--color-primary-darker);
  --shadow-success: var(--shadow-success);
  --shadow-error: var(--shadow-error);
  --shadow-info: var(--shadow-info);
  --shadow-surface-brand: var(--shadow-surface-brand);
}
