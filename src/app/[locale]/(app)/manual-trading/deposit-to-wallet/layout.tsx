import type { ReactNode } from 'react';

import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { DepositToWalletsHeader } from '@/module/deposit-to-wallet/deposit-to-wallet-header';

export default async function ManualTradingLayout({ children }: { children: ReactNode }) {
  const leagueSystemEnabled = await getRemoteConfigValue('league_system_enabled');

  return (
    <>
      <DepositToWalletsHeader leagueSystemEnabled={leagueSystemEnabled} />
      {children}
    </>
  );
}
