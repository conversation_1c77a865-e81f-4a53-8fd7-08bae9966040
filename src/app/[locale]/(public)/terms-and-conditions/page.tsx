import { getTranslations, setRequestLocale } from 'next-intl/server';

import { ROUTES } from '@/constants/routes';
import { formatRichText } from '@/lib/formatters/format-rich-text';
import { BackLink } from '@/module/routing/back-link';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('terms-and-conditions'),
  };
}

const messages = [
  {
    heading: 'key-concepts.title',
    content: [
      'key-concepts.account',
      'key-concepts.affiliates',
      'key-concepts.laws',
      'key-concepts.blockchain',
      'key-concepts.conduct',
      'key-concepts.confidential',
      'key-concepts.digital-assets',
      'key-concepts.gas-fees',
      'key-concepts.fatbot',
      'key-concepts.fatbot-channels',
      'key-concepts.materials',
      'key-concepts.parties',
      'key-concepts.personal-information',
      'key-concepts.private-keys',
      'key-concepts.services',
      'key-concepts.smart-contracts',
      'key-concepts.user',
      'key-concepts.wallet',
    ],
  },
];

export default async function TermsAndConditions() {
  setRequestLocale('en');
  const t = await getTranslations('terms-and-conditions');

  return (
    <div className="mx-auto max-w-(--breakpoint-lg) text-md leading-18 text-text-secondary sm:py-7">
      <div className="flex flex-col gap-y-5 rounded-lg bg-surface-area p-3">
        <BackLink defaultUrl={ROUTES.PROFILE.ROOT} />
        <h1 className="text-display-l font-bold text-text-primary">{t('title')}</h1>
        <p>{t('text')}</p>
        <ol className="nested-counter list-inside space-y-2 leading-relaxed">
          {/* Article 1 */}
          <li className="space-y-2">
            <h2 className="inline text-lg font-semibold text-text-primary">{t('key-concepts.title')}</h2>
            <ol className="list-inside">
              {messages[0]?.content.map((key, index) => <li key={index}>{t.rich(key, formatRichText)}</li>)}
            </ol>
          </li>
          {/* Article 2 */}
          <li className="space-y-2">
            <h2 className="inline text-lg font-semibold text-text-primary">{t('core-provisions.title')}</h2>
            <ol className="list-inside">
              <li>
                {t.rich('core-provisions.foundation', formatRichText)}
                <ol>
                  <li>{t.rich('core-provisions.binding-terms', formatRichText)}</li>
                  <li>{t.rich('core-provisions.unified-agreement', formatRichText)}</li>
                  <li>{t.rich('core-provisions.updates', formatRichText)}</li>
                  <li>
                    {t.rich('core-provisions.restrictions', formatRichText)}
                    <ul className="ml-2 list-inside list-disc">
                      <li>{t('core-provisions.restrictions-1')}</li>
                      <li>{t('core-provisions.restrictions-2')}</li>
                      <li>{t('core-provisions.restrictions-3')}</li>
                      <li>{t('core-provisions.restrictions-4')}</li>
                    </ul>
                  </li>
                </ol>
              </li>
              <li>
                {t.rich('core-provisions.limitations', formatRichText)}
                <ol>
                  <li>{t('core-provisions.limitations-1')}</li>
                  <li>{t('core-provisions.limitations-2')}</li>
                  <li>{t('core-provisions.limitations-3')}</li>
                  <li>{t('core-provisions.limitations-4')}</li>
                </ol>
              </li>
              <li>
                {t.rich('core-provisions.service-use-requirements', formatRichText)}
                <ol>
                  <li>{t('core-provisions.service-use-requirements-1')}</li>
                  <li>{t('core-provisions.service-use-requirements-2')}</li>
                  <li>{t('core-provisions.service-use-requirements-3')}</li>
                  <li>{t('core-provisions.service-use-requirements-4')}</li>
                  <li>{t('core-provisions.service-use-requirements-5')}</li>
                </ol>
              </li>
            </ol>
          </li>
          {/* Article 3 */}
          <li className="space-y-2">
            <h2 className="inline text-lg font-semibold text-text-primary">{t('services.title')}</h2>
            <ol className="list-inside">
              <li>
                {t.rich('services.functionalities', formatRichText)}
                <ul className="ml-2 list-inside list-disc">
                  <li>{t('services.functionalities-1')}</li>
                  <li>{t('services.functionalities-2')}</li>
                  <li>{t('services.functionalities-3')}</li>
                  <li>{t('services.functionalities-4')}</li>
                  <li>{t('services.functionalities-5')}</li>
                </ul>
              </li>
              <li>{t.rich('services.wallet-requirements', formatRichText)}</li>
              <li>{t.rich('services.fees', formatRichText)}</li>
            </ol>
          </li>
          {/* Article 4 */}
          <li className="space-y-2">
            <h2 className="inline text-lg font-semibold text-text-primary">{t('compliance.title')}</h2>
            <ol className="list-inside">
              <li>
                {t.rich('compliance.responsibilities', formatRichText)}
                <ol>
                  <li>{t.rich('compliance.responsibilities-1', formatRichText)}</li>
                  <li>{t.rich('compliance.responsibilities-2', formatRichText)}</li>
                  <li>{t.rich('compliance.responsibilities-3', formatRichText)}</li>
                </ol>
              </li>
              <li>{t.rich('compliance.disclaimer', formatRichText)}</li>
            </ol>
          </li>
          {/* Article 5 */}
          <li className="space-y-2">
            <h2 className="inline text-lg font-semibold text-text-primary">{t('dispute.title')}</h2>
            <ol className="list-inside">
              <li>{t.rich('dispute.waiver', formatRichText)}</li>
              <li>{t.rich('dispute.notice', formatRichText)}</li>
              <li>{t.rich('dispute.arbitration', formatRichText)}</li>
            </ol>
          </li>
          {/* Article 6 */}
          <li className="space-y-2">
            <h2 className="inline text-lg font-semibold text-text-primary">{t('termination.title')}</h2>
            <ol className="list-inside">
              <li>{t.rich('termination.termination', formatRichText)}</li>
              <li>{t.rich('termination.retention', formatRichText)}</li>
              <li>{t.rich('termination.amendments', formatRichText)}</li>
            </ol>
          </li>
          {/* Article 7 */}
          <li className="space-y-2">
            <h2 className="inline text-lg font-semibold text-text-primary">{t('additional-provisions.title')}</h2>
            <ol className="list-inside">
              <li>{t.rich('additional-provisions.jurisdiction', formatRichText)}</li>
              <li>{t.rich('additional-provisions.force', formatRichText)}</li>
              <li>{t.rich('additional-provisions.invalidity', formatRichText)}</li>
              <li>{t.rich('additional-provisions.transfer', formatRichText)}</li>
              <li>{t.rich('additional-provisions.communication', formatRichText)}</li>
            </ol>
          </li>
        </ol>
      </div>
    </div>
  );
}
