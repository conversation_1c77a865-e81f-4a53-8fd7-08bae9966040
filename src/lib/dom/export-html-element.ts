import { domToBlob, domToWebp, domToPng, domToCanvas, domToJpeg, domToSvg, type Options } from 'modern-screenshot';
import { match } from 'ts-pattern';

type Format = 'blob' | 'canvas' | 'jpeg' | 'png' | 'svg' | 'webp';

export const exportHtmlElement = async (
  element: HTMLElement | null,
  filename: string,
  format: Format = 'png',
  exportOptions?: Options,
) => {
  if (!element) return;

  const dataUrl = await match(format)
    .with('blob', async () => {
      const blob = await domToBlob(element, exportOptions);
      return URL.createObjectURL(blob);
    })
    .with('canvas', async () => {
      const canvas = await domToCanvas(element, exportOptions);
      return canvas.toDataURL();
    })
    .with('jpeg', () => domToJpeg(element, exportOptions))
    .with('png', () => domToPng(element, exportOptions))
    .with('svg', () => domToSvg(element, exportOptions))
    .with('webp', () => domToWebp(element, exportOptions))
    .exhaustive();

  const link = document.createElement('a');
  link.href = dataUrl;
  link.download = `${filename}.${format}`;
  link.click();
};
