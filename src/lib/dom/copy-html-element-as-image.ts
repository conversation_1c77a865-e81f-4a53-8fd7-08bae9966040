import { domToBlob, type Options } from 'modern-screenshot';

interface CopyHtmlElementAsImageOptions extends Options {
  onSuccess?: () => void;
  onError?: () => void;
}

export const copyHtmlElementAsImage = async (element: HTMLElement | null, options?: CopyHtmlElementAsImageOptions) => {
  if (!element) return;

  const dataUrl = await domToBlob(element, options);

  try {
    await navigator.clipboard.write([
      new ClipboardItem({
        'image/png': dataUrl,
      }),
    ]);
    options?.onSuccess?.();
  } catch {
    options?.onError?.();
  }
};
