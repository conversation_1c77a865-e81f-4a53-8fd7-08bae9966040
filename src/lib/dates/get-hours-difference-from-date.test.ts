import { addHours, formatISO, subHours } from 'date-fns';

import { getHoursDifferenceFromDate, isLessThanXHours } from './get-hours-difference-from-date';

const BASE_DATE = new Date('2024-06-01T12:00:00Z');

describe('getHoursDifferenceFromDate', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(BASE_DATE);
  });
  afterEach(() => {
    vi.useRealTimers();
  });

  it('returns 0 for undefined', () => {
    expect(getHoursDifferenceFromDate(undefined)).toBe(0);
  });

  it('returns 0 for null', () => {
    expect(getHoursDifferenceFromDate(null)).toBe(0);
  });

  it('returns 0 for now', () => {
    expect(getHoursDifferenceFromDate(formatISO(BASE_DATE))).toBe(0);
  });

  it('returns positive hours for a date in the future', () => {
    const future = addHours(BASE_DATE, 5);
    expect(getHoursDifferenceFromDate(formatISO(future))).toBe(5);
  });

  it('returns negative hours for a date in the past', () => {
    const past = subHours(BASE_DATE, 3);
    expect(getHoursDifferenceFromDate(formatISO(past))).toBe(-3);
  });
});

describe('isLessThanXHours', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(BASE_DATE);
  });
  afterEach(() => {
    vi.useRealTimers();
  });

  it('returns false if date is undefined', () => {
    expect(isLessThanXHours(undefined, 1)).toBe(false);
  });

  it('returns false if date is null', () => {
    expect(isLessThanXHours(null, 1)).toBe(false);
  });

  it('returns false if targetHours is not a number', () => {
    const future = addHours(BASE_DATE, 2);
    expect(isLessThanXHours(formatISO(future), NaN)).toBe(false);
    expect(isLessThanXHours(formatISO(future), undefined as unknown as number)).toBe(false);
  });

  it('returns true if the date is less than X hours in the future', () => {
    const future = addHours(BASE_DATE, 2);
    expect(isLessThanXHours(formatISO(future), 3)).toBe(true);
  });

  it('returns false if the date is more than X hours in the future', () => {
    const future = addHours(BASE_DATE, 5);
    expect(isLessThanXHours(formatISO(future), 3)).toBe(false);
  });
});
