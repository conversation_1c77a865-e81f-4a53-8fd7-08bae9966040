import { type AreaData } from 'lightweight-charts';

import { timeStampFormatter } from '@/components/charts/utils/timestamp-formatter';
import type { GetTokenPriceChartResult } from '@/lib/api';

export interface TokenChartData {
  volume: AreaData[];
  price: {
    area: AreaData[];
  };
}

export const formatTokenChartData = (data: GetTokenPriceChartResult['data']): TokenChartData =>
  data.reduce(
    (accumulator, current) => {
      const time = timeStampFormatter(current.timestamp);

      return {
        volume: [
          ...accumulator.volume,
          {
            time,
            value: Number(current.volume),
          },
        ],
        price: {
          area: [
            ...accumulator.price.area,
            {
              time,
              value: Number(current.close),
            },
          ],
        },
      };
    },
    {
      volume: [] as AreaData[],
      price: {
        area: [] as AreaData[],
      },
    },
  );
