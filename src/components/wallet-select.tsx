import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { Wallet2 } from '@/assets/wallet-2';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/form/select';
import { useSortedWallets } from '@/hooks/use-sorted-wallets';
import { type GetAllUserWalletsResult } from '@/lib/api';
import { formatNumber } from '@/lib/formatters/format-number';
import { cn } from '@/lib/utils';
import { makeWalletLabel } from '@/lib/wallet/make-wallet-label';
import { useNativePosition } from '@/module/assets/use-native-position';
import { ChainIcon } from '@/module/multichain/chain-icon';

interface Props {
  selected?: string | null;
  onSelect?: (walletId: string) => void;
  inline?: boolean;
  displayWalletBalance?: boolean;
  wallets?: GetAllUserWalletsResult[];
  loading?: boolean;
  classNames?: {
    walletName?: string;
  };
}

const WalletSelectItem = ({
  wallet,
  displayWalletBalance,
  isSelected,
}: {
  wallet: GetAllUserWalletsResult;
  displayWalletBalance: boolean;
  isSelected: boolean;
}) => {
  const { walletId, chain } = wallet;

  const { data } = useNativePosition(
    { walletId },
    {
      enabled: displayWalletBalance,
      staleTime: 0,
    },
  );

  const nativeBalance = data?.tokenNativeAmount ? `${formatNumber(data.tokenNativeAmount)} ${data.tokenSymbol}` : '';

  return (
    <SelectItem
      key={walletId}
      className={cn('rounded-xs focus:bg-surface-third-layer focus:text-white', {
        'text-white': isSelected,
      })}
      itemTextAsChild={true}
      value={walletId}
    >
      <div className="flex w-full items-center justify-between gap-2">
        <div className="flex items-center gap-1">
          <ChainIcon chain={chain} className="size-2" />
          <span className="inline-block font-semibold">{makeWalletLabel(wallet)}</span>
        </div>
        {nativeBalance ? <span className="inline-block">{nativeBalance}</span> : null}
      </div>
    </SelectItem>
  );
};

export const WalletSelect: React.FC<Props> = ({
  selected,
  onSelect,
  inline,
  displayWalletBalance = false,
  wallets = [],
  loading,
  classNames,
}) => {
  const t = useTranslations();

  const selectedWallet = useMemo(() => wallets.find((wallet) => wallet.walletId === selected), [wallets, selected]);

  const sortedWallets = useSortedWallets(wallets);

  return (
    <Select value={selected ?? ''} onValueChange={onSelect}>
      <SelectTrigger
        className={cn(
          'select-none border-none text-md font-semibold text-text-secondary hover:text-white/70 active:text-white/70',
          inline && 'w-auto',
        )}
        disabled={loading}
      >
        <div className="flex items-center gap-x-1">
          {selectedWallet?.chain ? <ChainIcon chain={selectedWallet.chain} className="size-2" /> : <Wallet2 />}
          <SelectValue placeholder={t('common.all-wallets')}>
            <div className={cn('max-w-40 truncate', classNames?.walletName)}>{selectedWallet?.customName}</div>
          </SelectValue>
        </div>
      </SelectTrigger>
      <SelectContent align="end">
        {sortedWallets.map((wallet) => (
          <WalletSelectItem
            key={wallet.walletId}
            displayWalletBalance={displayWalletBalance}
            isSelected={selected === wallet.walletId}
            wallet={wallet}
          />
        ))}
      </SelectContent>
    </Select>
  );
};
