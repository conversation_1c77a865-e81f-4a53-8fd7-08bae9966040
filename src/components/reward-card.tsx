import type { FunctionComponent, ReactNode } from 'react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface RewardCardProps {
  title: string;
  description: string;
  onClick: () => void;
  children?: ReactNode;
  className?: string;
}

export const RewardCard: FunctionComponent<RewardCardProps> = ({
  title,
  description,
  onClick,
  children,
  className,
}) => (
  <Card>
    <h2 className="text-lg font-semibold text-neutral-900">{title}</h2>
    <span className="text-sm text-neutral-500">{description}</span>
    <Button className={className} onClick={onClick}>
      {children}
    </Button>
  </Card>
);
