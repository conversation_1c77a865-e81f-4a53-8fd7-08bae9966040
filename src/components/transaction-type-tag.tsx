'use client';

import { useTranslations } from 'next-intl';
import { useMemo } from 'react';
import { match } from 'ts-pattern';

import { ChainBadge } from '@/components/chain-badge';
import {
  BotTransactionType as BotTransactionTypeEnum,
  type Chain,
  TransactionStatus as TransactionStatusType,
  TransactionType as TransactionTypeEnum,
} from '@/lib/api';
import { cn } from '@/lib/utils';

import { StatusIndicator, type StatusIndicatorStatus } from './status-indicator';

interface Props {
  type: BotTransactionTypeEnum | TransactionTypeEnum;
  chain?: Chain;
  status?: TransactionStatusType;
}

export const TransactionTypeTag: React.FC<Props> = ({ type, status, chain }) => {
  const t = useTranslations();

  const isBotTransaction = useMemo(
    () =>
      type === BotTransactionTypeEnum.DEPOSIT ||
      type === BotTransactionTypeEnum.PORTFOLIO_WITHDRAW ||
      type === BotTransactionTypeEnum.APPROVE,
    [type],
  );

  const transactionTypeText = match(type.toUpperCase())
    .with(TransactionTypeEnum.BUY, () => t('common.buy'))
    .with(TransactionTypeEnum.SELL, () => t('common.sell'))
    .with(TransactionTypeEnum.TRANSFER_TOKEN, () => t('common.sent'))
    .with(TransactionTypeEnum.TRANSFER_CURRENCY, () => t('common.sent'))
    .with(BotTransactionTypeEnum.PORTFOLIO_WITHDRAW, () => t('common.withdraw'))
    .with(BotTransactionTypeEnum.APPROVE, () => t('common.approve'))
    .with(BotTransactionTypeEnum.DEPOSIT, () => t('common.deposit'))
    .otherwise(() => '');

  const indicatorStatus = match(status)
    .with(TransactionStatusType.PENDING, (): StatusIndicatorStatus => 'warning')
    .with(TransactionStatusType.SUCCESS, (): StatusIndicatorStatus => 'success')
    .with(TransactionStatusType.FAILED, (): StatusIndicatorStatus => 'error')
    .with(TransactionStatusType.NOT_LANDED, (): StatusIndicatorStatus => 'error')
    .otherwise(() => undefined);

  return (
    <div
      className={cn(
        'relative flex size-5 min-h-5 min-w-5 items-center justify-center rounded-xxs p-0 text-xs font-bold capitalize',
        {
          'bg-event-success-background text-event-success-content': type === TransactionTypeEnum.BUY,
          'bg-event-error-background text-event-error-content': type === TransactionTypeEnum.SELL,
          'bg-event-warning-background text-event-warning-content':
            type === TransactionTypeEnum.TRANSFER_TOKEN || type === TransactionTypeEnum.TRANSFER_CURRENCY,
          'bg-[#FFDA6333] text-text-active': type === BotTransactionTypeEnum.DEPOSIT,
          'bg-[#8053FF33] text-[#AE92FF]': type === BotTransactionTypeEnum.PORTFOLIO_WITHDRAW,
        },
        {
          'break-all text-center p-[7px]': isBotTransaction,
          'pr-1': type === BotTransactionTypeEnum.DEPOSIT,
        },
      )}
    >
      <span
        className={cn('text-left leading-1.5 tracking-normal', {
          'text-xxs w-full break-words': isBotTransaction,
        })}
      >
        {transactionTypeText}
      </span>
      {chain ? <ChainBadge chain={chain} className="absolute -bottom-0.5 -right-0.5" /> : null}
      {indicatorStatus ? (
        <StatusIndicator
          animate={status === TransactionStatusType.PENDING}
          className="absolute right-0 top-0"
          status={indicatorStatus}
        />
      ) : null}
    </div>
  );
};
