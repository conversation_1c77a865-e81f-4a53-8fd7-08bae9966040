'use client';
import * as TabsPrimitive from '@radix-ui/react-tabs';
import { cva, type VariantProps } from 'class-variance-authority';
import type { ComponentProps } from 'react';

import { cn } from '@/lib/utils';

const Tabs = TabsPrimitive.Root;

const tabsListVariants = cva(
  'inline-flex w-min items-center justify-center rounded-full border border-border-subtle bg-surface-area p-0.5',
  {
    variants: {
      variant: {
        primary: '',
        underline: 'w-full mb-[-6px] flex w-full justify-start border-none bg-transparent',
        'user-chain': '',
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  },
);

interface TabsListProps extends React.ButtonHTMLAttributes<HTMLDivElement>, VariantProps<typeof tabsListVariants> {}

const TabsList = ({ className, variant, ...props }: TabsListProps) => (
  <TabsPrimitive.List className={cn(tabsListVariants({ variant, className }))} {...props} />
);
TabsList.displayName = TabsPrimitive.List.displayName;

const tabsTriggerVariants = cva(
  'inline-flex flex-1! items-center justify-center whitespace-nowrap rounded-full px-3 py-1.5 font-goldplay text-base font-semibold uppercase text-text-secondary ring-offset-white transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-surface-third-layer data-[state=active]:text-text-primary',
  {
    variants: {
      variant: {
        primary: '',
        underline: cn(
          'rounded-none bg-transparent! px-2 py-0.5 text-md font-medium text-md font-semibold normal-case',

          // text color
          'text-text-secondary',
          'data-[state=active]:text-text-primary',

          // border
          'border-b-2 border-b-surface-subtle',
          'data-[state=active]:border-b-surface-brand-1',
        ),
        userChain: '!none',
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  },
);

export interface TabsTriggerProps
  extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>,
    VariantProps<typeof tabsTriggerVariants> {
  inline?: boolean;
}

const TabsTrigger = ({ className, variant, inline, ...props }: TabsTriggerProps) => (
  <TabsPrimitive.Trigger
    className={cn(tabsTriggerVariants({ variant, className }), inline && 'flex-none!')}
    {...props}
  />
);
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = ({ className, ...props }: ComponentProps<typeof TabsPrimitive.Content>) => (
  <TabsPrimitive.Content className={cn('ring-offset-white focus-visible:outline-hidden', className)} {...props} />
);
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsContent, TabsList, TabsTrigger };
