/* eslint-disable @typescript-eslint/restrict-plus-operands */
import { useCallback, useEffect, useRef, useState } from 'react';
import { type FieldValues, useFormContext, type UseFormSetValue } from 'react-hook-form';
import type { NumberFormatValues, SourceInfo } from 'react-number-format';
import { useDebounceCallback } from 'usehooks-ts';
import { v4 as uuidv4 } from 'uuid';

import { formatPercentage } from '@/lib/formatters/format-percentage';

import { isSingleValueSlider, isStringValueStringArray, type SliderType, type SliderValue } from './types';

const DEBOUNCE_MS = 200;

export const getSliderRange = (data: number[]) => ({
  min: data[0],
  max: data[data.length - 1],
  // we can do this, bcs data is sorted and have equidistant gaps
  gap: (data[1] ?? 0) - (data[0] ?? 0),
});

interface Args {
  name: string;
  type?: SliderType;
  sliderMax: number;
  displayPercentage?: boolean;
}

const generateRandomKey = () => uuidv4();
export const getFormattedChartInputValue = (value: number | undefined, percentage?: boolean) => {
  if (value === Infinity) {
    return null;
  }

  if (!percentage || value == null) {
    return value;
  }

  // percentage value
  return formatPercentage(value, 0);
};

// Number('Infinity') is valid Infinity
export const STRING_INFINITY = 'Infinity';
export const EMPTY_VALUE = 'NaN';

export const useSliderInputs = ({ name, type, sliderMax, displayPercentage = false }: Args) => {
  const { watch, setValue, trigger } = useFormContext();
  const [startValue, endValue] = watch(name) as SliderValue;
  const startInputRef = useRef<HTMLInputElement | null>(null);
  const endInputRef = useRef<HTMLInputElement | null>(null);
  const [focusedInput, setFocusedInput] = useState<'' | 'end' | 'start'>('');
  // always available
  const startValueNumber = Number(startValue);
  const endValueNumber = isSingleValueSlider(type) ? undefined : Number(endValue);

  // forces re-render of numeric inputs in certain cases
  const [generatedInputKey, setGeneratedInputKey] = useState('');

  const setFormValue: UseFormSetValue<FieldValues> = async (name, value, options) => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
    const stringValue = value.map(String);

    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    setValue(name, stringValue, options);

    // of type is 'singleValue' only startValue is rendered (in this case startValue can be Infinity)
    const valueToCompare = isSingleValueSlider(type) ? startValueNumber : endValueNumber;

    // if the endValue is Infinity or we overflow end value two times in a row (inputValue is still the same), so we need to force re-render
    if (valueToCompare === Infinity || startValue + 1 === endValue) {
      setGeneratedInputKey(generateRandomKey());
    }

    await trigger(name);
  };

  useEffect(() => {
    if (generatedInputKey) {
      if (focusedInput === 'start') {
        startInputRef.current?.focus();
      } else if (focusedInput === 'end') {
        endInputRef.current?.focus();
      }
    }
  }, [generatedInputKey, focusedInput]);

  const debouncedSetValue = useDebounceCallback(setFormValue, DEBOUNCE_MS);

  const onStartValueChange = useCallback(
    (value: NumberFormatValues, sourceInfo: SourceInfo) => {
      const { floatValue } = value;

      // this is not problem at all, because prop change won't execute setValue
      if (sourceInfo.source === ('prop' as SourceInfo['source'])) {
        return;
      }

      if (floatValue == null) {
        const newNullValue = isSingleValueSlider(type) ? [] : [NaN, endValueNumber];

        debouncedSetValue(name, newNullValue);
        return;
      }

      // we type into input number, e.g. 20, but we need to convert to a real value (0.2)
      const newValue = displayPercentage ? floatValue / 100 : floatValue;

      // when type is singleValue, we need to handle  startValue separately, bcs only this input is rendered
      if (isSingleValueSlider(type)) {
        debouncedSetValue(name, [newValue]);
        return;
      }

      const end = endValueNumber as number;

      // if toValue is more or equal to maxValue, we need subtract 1 (so they are not equal)
      if (newValue >= end) {
        debouncedSetValue(name, [endValueNumber !== Infinity ? end - 1 : sliderMax, endValueNumber]);
        return;
      }

      debouncedSetValue(name, [newValue, endValueNumber]);
    },
    [debouncedSetValue, name, type, endValueNumber, sliderMax, displayPercentage],
  );

  const onEndValueChange = useCallback(
    (value: NumberFormatValues, sourceInfo: SourceInfo) => {
      const { floatValue } = value;

      // this is not problem at all, because prop change won't execute setValue
      if (sourceInfo.source === ('prop' as SourceInfo['source'])) {
        return;
      }

      if (floatValue == null) {
        debouncedSetValue(name, [startValueNumber, NaN]);
        return;
      }

      const newValue = displayPercentage ? floatValue / 100 : floatValue;

      debouncedSetValue(name, [startValueNumber, newValue]);
    },
    [debouncedSetValue, name, startValueNumber, displayPercentage],
  );

  const onStartInputFocus = () => {
    setFocusedInput('start');
  };

  const onEndInputFocus = () => {
    setFocusedInput('end');
  };

  const clearFocus = () => {
    setFocusedInput('');
    void trigger(name);
  };

  return {
    startValueNumber,
    onStartValueChange,
    endValueNumber,
    onEndValueChange,
    generatedInputKey,
    onStartInputFocus,
    onEndInputFocus,
    startInputRef,
    endInputRef,
    trigger,
    focusedInput,
    clearFocus,
    setFormValue: debouncedSetValue,
  };
};

export const getSliderNumberValues = (value: unknown): number[] => {
  if (isStringValueStringArray(value)) {
    return value.reduce<number[]>((accumulator, value) => {
      if (isNaN(parseFloat(value))) {
        return accumulator;
      }

      return [...accumulator, parseFloat(value)];
    }, []);
  }

  return [];
};
