import type { ReactNode } from 'react';
import type { NumericFormatProps } from 'react-number-format';

import type { InputProps } from '@/components/form/input';
import { NumericInput } from '@/components/form/numeric-input';
import { TokenImage } from '@/components/token-image';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export interface Option {
  icon?: React.ReactNode;
  label: string;
  value: number;
}

interface Props {
  className?: string;
  title: string;
  isLoading?: boolean;
  variant?: 'primary' | 'secondary';
  shadowVariant?: 'flat' | 'primary';
  tokenName?: string;
  tokenImage?: string;
  helpText?: ReactNode;
  extraActions?: ReactNode;
  presets?: {
    options: Option[];
    onOptionSelect: (option: Option) => void;
  };
  inputProps?: Omit<NumericFormatProps, 'className' | 'customInput'>;
  errors?: ReactNode;
}

const Input = ({ className, type, ...props }: InputProps) => (
  <input
    spellCheck={false}
    type={type}
    {...props}
    className={cn(
      'peer flex h-5 w-full bg-transparent p-0 text-display-s font-bold transition-colors focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50',
      className,
    )}
  />
);

export const TokenInput = ({
  title,
  isLoading,
  tokenName,
  tokenImage,
  inputProps,
  presets,
  helpText,
  variant,
  errors,
  className,
  extraActions,
  shadowVariant = 'primary',
}: Props) => (
  <>
    <Card
      className={cn('relative z-10 gap-1 ', shadowVariant === 'primary' ? 'shadow-primary' : '', className)}
      variant={variant ?? 'primary'}
    >
      <p className="text-sm font-medium text-text-secondary">{title}</p>
      <div className="flex items-center gap-2">
        <div className={cn('flex-1', { 'animate-intense-pulse': isLoading })}>
          <NumericInput
            allowNegative={false}
            inputMode="decimal"
            thousandSeparator={true}
            {...inputProps}
            allowLeadingZeros={false}
            allowedDecimalSeparators={[',']}
            customInput={Input}
          />
        </div>
        <div className="flex items-center gap-1 rounded-full bg-surface-third-layer p-1 pr-2">
          <div className="size-3">
            <TokenImage alt={tokenName ?? ''} height={24} src={tokenImage} width={24} />
          </div>
          <div className="text-md font-semibold">{tokenName}</div>
        </div>
      </div>
      {helpText != null ? <div className="truncate text-sm font-medium text-text-secondary">{helpText}</div> : null}
      {extraActions}
    </Card>
    {errors}
    {presets ? (
      <div className="mt-1 flex flex-wrap gap-1">
        {presets.options.map((item) => (
          <button
            key={item.label}
            className="flex-1"
            type="button"
            onClick={() => {
              presets.onOptionSelect(item);
            }}
          >
            <Card
              className="w-full py-1 text-text-secondary shadow-primary transition-colors hover:bg-surface-active hover:text-text-invert"
              variant="primary"
            >
              <p className="text-sm font-semibold">{item.label}</p>
            </Card>
          </button>
        ))}
      </div>
    ) : null}
  </>
);
