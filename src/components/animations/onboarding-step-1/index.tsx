'use client';

import { useEffect, useState } from 'react';

import { OnboardingAnimationStep1Loading } from './onboarding-step-1-loading';

interface Props {
  className?: string;
}

// eslint-disable-next-line @typescript-eslint/consistent-type-imports
type State = typeof import('./onboarding-step-1-lottie');

export const OnboardingAnimationStep1 = (props: Props) => {
  const [module, setModule] = useState<State>();

  useEffect(() => {
    void import('./onboarding-step-1-lottie').then((module) => {
      setModule(module);
    });
  }, []);

  if (module) {
    return <module.OnboardingAnimationStep1Lottie {...props} />;
  }

  return <OnboardingAnimationStep1Loading className={props.className} />;
};
