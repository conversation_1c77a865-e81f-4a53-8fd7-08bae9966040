'use client';

import { useEffect, useState } from 'react';

import { FireworksAnimationLoading } from './fireworks-animation-loading';

interface Props {
  className?: string;
}

// eslint-disable-next-line @typescript-eslint/consistent-type-imports
type State = typeof import('./fireworks-animation-lottie');

export const FireworksAnimation = (props: Props) => {
  const [module, setModule] = useState<State>();

  useEffect(() => {
    void import('./fireworks-animation-lottie').then((module) => {
      setModule(module);
    });
  }, []);

  if (module) {
    return <module.FireworksAnimationLottie {...props} />;
  }

  return <FireworksAnimationLoading className={props.className} />;
};
