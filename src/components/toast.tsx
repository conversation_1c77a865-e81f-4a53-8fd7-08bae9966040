'use client';

import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';
// eslint-disable-next-line no-restricted-imports
import { toast as sonnerToast } from 'sonner';

import { AlertTriangle, AnnotationInfo, AnnotationX, CloseX, Trophy } from '@/assets';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ToastProps {
  id: number | string;
  title: string;
  message?: string;
  variant: Variant;
  button?: {
    label: string;
    onClick: () => void;
  };
}

type Variant = NonNullable<VariantProps<typeof variants>['variant']>;

const iconMap: Record<Variant, React.ReactNode> = {
  info: <AnnotationInfo className="size-3" />,
  error: <AnnotationX className="size-3" />,
  warning: <AlertTriangle className="size-3" />,
  success: <Trophy className="size-3" />,
};

const variants = cva('w-full gap-2 rounded-md p-2 flex flex-col rounded-md', {
  variants: {
    variant: {
      info: 'bg-surface-card-bg text-text-active',
      error: 'bg-event-error-solidbg text-event-error-content',
      warning: 'bg-event-warning-solidbg text-event-warning-content',
      success: 'bg-event-success-solidbg text-event-success-content',
    },
  },
  defaultVariants: {
    variant: 'warning',
  },
});

const Toast = ({ title, variant, button, message, id }: ToastProps) => (
  <div className={cn(variants({ variant }))}>
    <div className="grid grid-cols-[24px_1fr] gap-1">
      {iconMap[variant]}
      <div
        className={cn('flex items-start justify-between gap-1', {
          'items-center': !message,
        })}
      >
        <div className="flex flex-col justify-between">
          <span className="text-sm font-bold">{title}</span>
          {message ? <div className="text-sm font-medium">{message}</div> : null}
        </div>
        <button type="button" onClick={() => sonnerToast.dismiss(id)}>
          <CloseX className="size-3 cursor-pointer" />
        </button>
      </div>
    </div>
    {button ? <Button onClick={button.onClick}>{button.label}</Button> : null}
  </div>
);

type ToastData = Omit<ToastProps, 'id' | 'title' | 'variant'>;

export const toast = {
  info: (title: string, data?: ToastData) =>
    sonnerToast.custom((id) => <Toast id={id} title={title} {...data} variant="info" />),
  error: (title: string, data?: ToastData) =>
    sonnerToast.custom((id) => <Toast id={id} title={title} {...data} variant="error" />),
  warning: (title: string, data?: ToastData) =>
    sonnerToast.custom((id) => <Toast id={id} title={title} {...data} variant="warning" />),
  success: (title: string, data?: ToastData) =>
    sonnerToast.custom((id) => <Toast id={id} title={title} {...data} variant="success" />),
};
