{"copy": "Copy", "cancel": "Cancel", "close": "Close", "done": "Done", "skip": "<PERSON><PERSON>", "clipboard": {"success": "Copied to clipboard!", "error": "Unable to copy to clipboard!"}, "chains": {"main": "Main", "wallet": "Wallet", "wallet-limit-reached": "Wallet limit reached"}, "metadata": {"default": "<PERSON><PERSON>", "league": {"root": "Fatbot | Streak Strategy", "claiming": "Fatbot | Claiming"}, "root": "Fatbot | League", "reset-password": "Fatbot | Reset Password", "manual-trading": {"root": "Fatbot | Manual Trading", "transactions": "Fatbot | Last Transactions", "my-assets": "Fatbot | My Assets", "deposit-to-wallet": "Fatbot | Deposit to Wallet", "wallet-withdraw": "Fatbot | Withdraw", "token-detail": "Fatbot | Token Details", "bot-launch": "Fatbot | Bot Launch"}, "profile": "Fatbot | User Profile", "setup-mfa": "Fatbot | Setup Two-Factor Authentication", "wallets": {"root": "Fatbot | Wallets", "create": "Fatbot | Create Wallet", "import": "Fatbot | Import Wallet", "done": "Fatbot | Done", "wallet-detail": "Fatbot | Wallet Detail", "private-key-copied": "Private key copied to clipboard"}, "asset-allocation": {"root": "Fatbot | Asset Allocation", "manual-traded": "Fatbot | Manually Traded"}, "bot-trading": {"root": "Fatbot | Bot Trading", "transactions": "Fatbot | Bot Transactions", "portfolio": "Fatbot | Bot Portfolio", "trades": "Fatbot | Bot Trades", "trade-detail": "Fatbot | Bot Trade Detail", "leaderboard": "Fatbot | Bot Portfolio", "compare": "Fatbot | Bot Compare", "bot-settings": "Fatbot | Bo<PERSON>s", "wizard": "Fatbot | Bot Wizard", "detail": "Fatbot | Bot Detail"}, "active-bots": "Fatbot | Active Bots", "referral": "Fatbot | Referral", "invite": "Fatbot | Invite Friends", "not-found": "Not Found", "sign-in": "Fatbot | Sign in", "sign-up": "Fatbot | Sign up", "privacy-policy": "Fatbot | Privacy Policy", "verify-email": "Fatbot | Verify Email", "terms-and-conditions": "Fatbot | Terms and Conditions"}, "league": {"streak-strategy": "Fatbot | Streak Strategy", "month-overview": {"my-donuts-this-month": "My Donuts This Month", "month-amount": "🍩 {amount}", "descritpion": "Every trade stacks more 🍩 Donuts—keep earning and maximize your rewards!", "swap-your-donuts": "Swap your  🍩  for $FATTY in:"}, "unlocked-fatty": {"your-unlocked-fatty": "Your Unlocked $FATTY", "total-earned": "Total Earned:", "fatty": "$FATTY", "claimable-now": "Claimable now", "claim-your-fatty": "Claim your $fatty"}, "streak": {"twelve-day-streak": "🔥 12-Day Streak", "at-risk": "At Risk", "time-left": "{hours}H {minutes}M left! Trade now to save your multiplier!"}, "weekly-fatty-league": {"weekly-fatty-league": "Weekly Fatty League", "see-all": "See all", "description": "Climb the ranks, stack 🍩, and earn more $FATTY—faster transactions, bigger profits!", "remaining-time-text": "{time} – Learn how it works", "you": "You", "donut-count": "🍩 {count}", "trade-x-more": "Trade ${amount} more for Top 250 & a bigger 🍩 multiplier!", "volume": "Volume ${volume}"}, "cards": {"title": "{amount} New Cards to Open!", "title-no-cards": "No new cards to open.", "see-all": "See all", "text": "Every $1,000 of daily volume traded gets you a card. Win big 🍩 rewards!", "trade-to-unlock": "Trade ${amount} more to unlock your next card!", "start-trading": "Start Trading & Stack Donuts!", "all-cards-collected": "All {amount}/{amount} Cards Collected!"}, "league-overview": {"heading": "Fatty League", "see-all": "See all", "description": "Climb the ranks, stack 🍩, and earn more $FATTY—faster transactions, bigger profits!", "my-donuts-this-month": "My Donuts This Month", "streak": "Streak", "x-day-streak": "🔥 {days}-Day", "claimable-cards": "Claimable Cards"}}, "asset-allocation": {"title": "Asset Allocation", "manual-traded": "Manually Traded", "on-chain": "on {chain}"}, "mfa": {"mfa": "Two-Factor Authentication", "setup-mfa": "Setup Two-Factor Authentication", "description": "For increased security, set up 2FA (two-factor authentication).", "link": "Setup 2FA", "page-title": "Secure Your Account with 2FA", "enter-mfa": "Enter your one time password below.", "mfa-invalid": "Your 2FA password must be exactly 6 digits long.", "are-you-sure": "Enable Two-Factor Authentication (2FA) to add an extra layer of security to your account.", "setup": "Activate Now", "cancel-and-return": "Cancel and Return", "instructions-1": "Scan this QR code using your authenticator app. Recommended: Google Authenticator or Microsoft Authenticator.", "instructions-2": "If your authenticator app does not recognize this QR code, you can manually enter the secret key below:", "instructions-3": "Once you have connected your authenticator, click the continue button below.", "continue": "Continue", "confirm-credentials": "Confirm your credentials below", "email": "Email", "password": "Password", "confirm": "Confirm", "success": "Success!", "success-message": "Your 2FA is all set up.", "back-to-login": "Log in again", "verify": "Verify", "totp-placeholder": "6-Digit Code", "invalid-user": "This email does not belong to this account. Please reauthenticate with your current credentials.", "enter-verification-code": "Enter verification code", "verification-description": "Enter the 6-digit code from your authenticator app to proceed. For security reasons, you will be logged out from all devices, including the one.", "success-heading": "Two-Factor Authentication Enabled", "success-text": "Extra security has been added to your account. For your safety, you will be logged out of all devices.", "mfa-enabled-title": "2FA is enabled", "mfa-enabled": "Two-Factor Authentication is currently enabled. Would you like to disable it?🔒 For security reasons, you will be logged out of all devices if you proceed.", "disable-mfa": "Disable 2FA", "confirm-disable": "Are you sure?", "disable-additional-information": "You can re-enable your two-factor authentication anytime you want.", "fatty-success-alt": "Success Fatty", "fatty-security-alt": "Security Fatty", "mfa-confirmation-subject": "Two-Factor Authentication Enabled on Your FATBOT Account", "secret-key": "Secret key", "secret-key-copied": "Secret key copied", "disable-mfa-error": "Failed to disable 2<PERSON>. Please try again.", "disable": "Reauthenticate to disable 2FA.", "password-required": "Password is required", "invalid-code": "Invalid verification code"}, "emails": {"send-error": "An error occurred sending your email.", "verify-your-email": "Verify your email address", "reset-your-password": "Reset your password", "user-does-not-exist": "User does not exist", "reset-password-error": "This email is not registered. Please sign up."}, "nav": {"overview": "Overview", "wallets": "Wallets", "account": "Account", "trade": "Trade", "sign-out": "Sign out"}, "charts": {"activity": {"transactions": "Transactions"}, "errors": {"empty-state": "No data found."}, "tooltip": {"date-time": "Date and time:", "chartkey": "Key:", "close": "Price", "volume": "Volume"}, "select-values": {"chart-keys": {"open": "Price", "price": "Price", "volume": "Volume"}, "time-range": {"hour": "1H", "day": "1D", "week": "1W", "month": "1M", "year": "1Y", "all": "All"}}}, "auth-error-messages": {"email-already-exists": "This email is already registered. Please use a different email.", "wrong-credentials": "Wrong password and/or email.", "wrong-password": "Wrong password.", "email-not-verified": "This email address has not been verified. Please check your email.", "too-many-requests": "Too many requests. Please try again later.", "password-policy-error": "Your password doesn't meet the required password policy. The password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (e.g.,", "expired-link": "Your reset password link has expired. Please request a new one.", "something-went-wrong": "Something went wrong. Please try again later.", "invalid-verification-code": "Invalid verification code", "invalid-password": "Invalid password. Please try again.", "too-many-attempts": "Too many attempts, try again later.", "unknown-error": "Some unknown error occurred.", "totp": "This account needs a TOTP for login."}, "verify-email": {"confirmation": "Verification successful!", "confirmation-text": "Your account has been set up successfully. Click the link below to sign in!", "separator": "Get started!", "cta": "Log in", "get-new-cta": "Get new email verification link", "expired": "Your link has expired. Please re-enter your password for <b>{email}</b> to receive a new one.", "password-for-user": "Password for {email}", "instructions": "All done! Check your email.", "link-expired": "Link expired!"}, "footer": {"copyright": "© Copyright 2024", "all-rights-reserved": "All Rights Reserved by <b> FATBOY </b>", "registration-address": "Registration: Václavské Náměstí 832/19, Prague 1, ", "czech-republic": "Czech Republic", "european-union": "European Union", "company-info": "CIN: ********, FATBOY s.r.o."}, "reset-password": {"title": "Reset password", "leading": "Enter the email address you used to create your account. We'll send a password reset email.", "leading-new-password": "Enter and confirm your new password below.", "email-placeholder": "Email address", "cta": "Reset password", "back-to-login": "Back to Login", "password-placeholder": "New password", "password-repeat-placeholder": "Repeat new password", "reset-password-cta": "Reset password", "validations": {"email-required": "Email is required", "email-format": "Email is not in the correct format", "password-required": "Password is required", "password-min-length": "Type at least {min} characters", "password-max-length": "Your password can have a maximum of {max} characters", "password-min": "Type at least 12 characters", "password-confirm-required": "Please confirm your password", "password-confirm-mismatch": "Passwords must match", "password-format": "The password must contain at least one uppercase letter, one lowercase letter, one number and one special character (i.e. "}, "on-success": "We've gone ahead and sent an email to <b>{email}</b>. Please check your inbox and follow instructions to reset your password.", "no-email-received": "Didn't receive an email?", "send-again": "Send again", "reset-password-email-sent": "An email will be sent if the address exists."}, "sign-up": {"sign-up": "Sign up to FATBOY", "email-placeholder": "Email address", "password-placeholder": "Password", "password-confirm-placeholder": "Confirm password", "checkbox-agree": "Sure, send me information about products, services, deals or recommendations by email", "cta": "Sign up", "already-have-account": "Already have an account?", "password-description": "Password must be {min}+ characters, include upper and lower case letters, one number and one symbol.", "disclaimer-link": "Terms of Service", "privacy-policy-link": "Privacy policy", "log-in": "Log in", "validations": {"email-required": "Email is required", "email-format": "Email is not in the correct format", "password-required": "Password is required", "password-min-length": "Type at least {min} characters", "password-max-length": "Your password can have a maximum of {max} characters", "password-min": "Type at least 12 characters", "password-confirm-required": "Please confirm your password", "password-confirm-mismatch": "Passwords must match", "password-format": "The password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (e.g., "}, "confirmation": "Thank you for signing up! A confirmation has been sent to <b>{email}</b>. Please follow the provided instructions to finish setting up your account.", "disclaimer": "By signing up you agree to FAT BOT's <a>Terms and Conditions</a> and <b>Privacy Policy</b>."}, "privacy-policy": {"title": "Privacy Policy", "introduction": "Introduction", "text-1": "Krel Central a.s. (“Company”, “us”, “our”, or “we”) respects your privacy and is committed to protecting it through compliance with this Privacy Policy. This policy describes how we collect, use, disclose, and protect your personal data when you use our application Fatbot (the “App”) and our website at https://www.fatty.io (the “Website”). Collectively, these are referred to as the “Services.”", "text-2": "By accessing or using the Services, you agree to the terms of this Privacy Policy. If you do not agree, please do not use the Services.", "text-3": "We may revise this Privacy Policy periodically to reflect changes in legal requirements, technology, or our practices. Please review this policy regularly to stay informed.", "collect": {"title": "Information We Collect", "text": "We may collect various types of information about you, including:", "personal-data": "Personal Data You Provide Directly", "identification-data": "<b>Identification Data:</b> Full name, address, email address, phone number, and other contact details.", "financial-data": "<b>Financial Data:</b> Cryptocurrency wallet IDs, transaction details, and associated fees.", "account-data": "<b>Account Data:</b> Username, passwords, and preferences.", "automatically-collected-data": "Automatically Collected Data", "device-information": "<b>Device Information:</b> IP address, browser type, operating system, device identifiers.", "usage-data": "<b>Usage Data:</b> Details about your interactions with the Services, including clicks, navigation paths, and time spent.", "location-data": "<b>Location Data:</b> Geographic location and GPS coordinates, where permitted.", "third-party-data-title": "Data from Third Parties", "third-party-data-text": "We may receive additional information from partners and service providers, such as analytics platforms, advertising networks, or social media platforms."}, "how-we-use-data": {"title": "How We Use Your Data", "text": "We use your information for the following purposes:", "provide": "<b>To Provide and Manage the Services:</b> Ensuring functionality, troubleshooting, and responding to inquiries.", "improve": "<b>To Improve the Services:</b> Analyzing trends, personalizing user experiences, and conducting research.", "security": "<b>For Security and Fraud Prevention:</b> Detecting and preventing unauthorized activity or access.", "comply": "<b>To Comply with Legal Obligations:</b> Meeting regulatory requirements, such as tax or anti-money laundering laws.", "marketing": "<b>Marketing and Advertising:</b> Providing relevant promotional content and offers (with your consent)."}, "sharing": {"title": "Sharing Your Information", "text": "We may share your information in the following circumstances:", "service-providers": "<b>With Service Providers:</b> Partners who perform services on our behalf, such as payment processors, analytics providers, or cloud hosting platforms.", "authorities": "<b>With Authorities:</b> As required by law or in response to valid legal requests.", "business-transactions": "<b>During Business Transactions:</b> In the event of a merger, acquisition, or sale of assets.", "consent": "<b>With Your Consent:</b> For purposes that you explicitly approve.", "disclaimer": "We do not sell your personal data to third parties."}, "cookies": {"title": "Cookies and Tracking Technologies", "text": "We use cookies, web beacons, and similar technologies to enhance your experience and gather information about how you use our Services. You can control cookie settings in your browser. Note that disabling cookies may limit some functionality."}, "data-security": {"title": "Data Security", "text": "We implement reasonable technical and organizational measures to protect your data from unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is completely secure. Your use of the Services is at your own risk."}, "rights": {"title": "Your Rights", "text": "Under applicable laws, you have the following rights:", "access": "<b>Access:</b> Request information about the personal data we hold about you.", "rectification": "<b>Rectification:</b> Correct inaccurate or incomplete data.", "erasure": "<b>Erasure:</b> Request deletion of your data where appropriate.", "restriction": "<b>Restriction:</b> Limit processing under certain conditions.", "portability": "<b>Portability:</b> Receive your data in a structured, commonly used format.", "objection": "<b>Objection:</b> Object to data processing based on legitimate interests.", "withdraw": "<b>Withdraw Consent:</b> Revoke your consent for specific uses of your data.", "disclaimer": "To exercise these rights, contact <NAME_EMAIL>."}, "data-retention": {"title": "Data Retention", "text": "We retain your data for as long as necessary to fulfill the purposes outlined in this Privacy Policy or comply with legal obligations. For example:", "transaction": "Transaction data: Retained for legal and accounting purposes.", "marketing": "Marketing data: Retained until you withdraw your consent.", "automatic": "Automatically collected data: Retained for up to 24 months."}, "international": {"title": "International Data Transfers", "text": "Your data may be transferred to and processed in countries outside of your jurisdiction. By using the Services, you consent to such transfers, provided appropriate safeguards are in place to protect your data."}, "children": {"title": "Children’s Privacy", "text": "Our Services are not intended for individuals under 18 years of age. We do not knowingly collect data from minors. If we learn that we have inadvertently collected such data, we will take steps to delete it."}, "contact": {"title": "Contact Information", "text": "If you have questions or concerns about this Privacy Policy, please contact us:", "email": "<b>Email:</b> <EMAIL>", "mail": "<b>Mail:</b> Krel Central a.s., Václavské náměstí 832/19, <PERSON><PERSON>, 110 00 Prague 1, Czech Republic"}, "changes": {"title": "Changes to This Privacy Policy", "text": "We reserve the right to update this Privacy Policy to reflect changes in our practices or legal requirements. The latest version will always be available on our Website, and significant changes will be communicated to you directly, where possible."}, "disclaimer": "By using the Website or Fatbot, users acknowledge and agree to this Privacy Policy."}, "terms-and-conditions": {"title": "Terms and Conditions", "text": "By accessing and using the Fatbot application, you confirm that you have read, understood, and accept these Terms, which are tailored specifically to Fatbot by Krel Central a.s., a company headquartered in Prague, Czech Republic (Václavské náměstí 832/19), VAT number: ********. These Terms represent a unique and comprehensive agreement governing your use of the Services. You acknowledge and agree that these Terms, along with the Service Provider's privacy policies (the \"Privacy Policy\"), govern your use of the Services.", "key-concepts": {"title": "Key Concepts for Fatbot Users", "account": "<b>\"Account\"</b> means a User's Fatbot account and any other accounts managed or controlled by the User related to the Services.", "affiliates": "<b>\"Affiliates\"</b> refers to any entity that controls, is controlled by, or is under common control with Krel Central a.s., the Service Provider.", "laws": "<b>\"Applicable Laws\"</b> means any laws, regulations, directives, and guidelines applicable to the Services, including local and international compliance requirements.", "blockchain": "<b>\"Blockchain\"</b> refers to a decentralized digital ledger technology used for recording transactions.", "conduct": "<b>\"Conduct Policy\"</b> refers to the Service Provider's rules governing acceptable behavior while using the Services.", "confidential": "<b>\"Confidential Information\"</b> has the meaning ascribed in Section 12.05.", "digital-assets": "<b>\"Digital Assets\"</b> means any digital currencies, tokens, or other cryptographic assets.", "gas-fees": "<b>\"Gas Fees\"</b> refers to transaction fees associated with Blockchain operations, which are not charged by the Service Provider but by the Blockchain network.", "fatbot": "<b>\"Fatbot\"</b> refers to the proprietary application developed by Krel Central a.s. for managing Digital Assets.", "fatbot-channels": "<b>\"Fatbot Channels\"</b> means the official channels and support services associated with Fatbot, such as the website www.fatty.io and other social networks.", "materials": "<b>\"Materials\"</b> include all intellectual property owned by or related to the Service Provider’s Services, including the Fatbot application, websites, and media.", "parties": "<b>\"Parties\"</b> means the User and the Service Provider.", "personal-information": "<b>\"Personal Information\"</b> refers to data collected to verify User identity as described in the Privacy Policy.", "private-keys": "<b>\"Private Keys\"</b> are cryptographic keys used to access and control Digital Assets within a Wallet.", "services": "<b>\"Services\"</b> refers to functionalities provided by Fatbot, including but not limited to digital asset management, automated trading, and Wallet operations.", "smart-contracts": "<b>\"Smart Contracts\"</b> refers to self-executing contracts coded onto Blockchain networks.", "user": "<b>\"User\"</b> means any individual or entity accessing or utilizing the Services.", "wallet": "<b>\"Wallet\"</b> means a digital tool for storing and managing Digital Assets."}, "core-provisions": {"title": "Core Provisions for Fatbot Usage", "foundation": "<b>Foundation of the Agreement</b>", "binding-terms": "<b>Binding Terms.</b> This document serves as a legally enforceable agreement between Krel Central a.s., headquartered in Prague, Czech Republic, and the User.", "unified-agreement": "<b>Unified Agreement.</b> The combined terms, alongside the Privacy Policy and Conduct Policy, represent the full understanding between the Service Provider and the User.", "updates": "<b>Updates to Terms.</b> Krel Central a.s. reserves the right to amend these Terms at its discretion. Significant updates will be announced within the Fatbot platform, and usage after such updates will signify consent.", "restrictions": "<b>Restrictions on Usage.</b> Users are prohibited from using Fatbot under the following conditions:", "restrictions-1": "Absence of legal capacity or authority to comply with these Terms;", "restrictions-2": "Residence in locations restricted by Fatbot policies;", "restrictions-3": "Prior suspension or removal from the platform;", "restrictions-4": "Engagement in activities that violate local or international laws.", "limitations": "<b>Limitations of the Service Provider</b>", "limitations-1": "Krel Central a.s. specializes in developing tools like Fatbot for advanced digital asset management.", "limitations-2": "The Service Provider does not take custody of Users' Digital Assets but enables operations through Smart Contracts and Wallets.", "limitations-3": "As a beta-stage service, Fatbot's functionalities may not always perform as expected, and changes to its features are ongoing.", "limitations-4": "Krel Central a.s. disclaims liability for any disruptions caused by external service providers, network issues, or Blockchain-specific challenges.", "service-use-requirements": "<b>Service Use Requirements</b>", "service-use-requirements-1": "Users must accept these Terms and associated policies, including the Privacy Policy and Conduct Policy, to access Fatbot.", "service-use-requirements-2": "The Service Provider reserves the right to mandate account registration for platform access and continued use.", "service-use-requirements-3": "Users bear full responsibility for protecting their login credentials, Wallet Private Keys, and any sensitive account information.", "service-use-requirements-4": "Any unauthorized access or suspicious activity must be promptly reported to the Fatbot support team.", "service-use-requirements-5": "All actions executed through a User’s account are solely the responsibility of that User."}, "services": {"title": "The Services", "functionalities": "<b>Fatbot Functionalities.</b> Fatbot allows Users to:", "functionalities-1": "Send and receive Digital Assets;", "functionalities-2": "Manage Wallets;", "functionalities-3": "Automate trades and set conditions for transactions;", "functionalities-4": "Monitor and control Blockchain fees;", "functionalities-5": "Access other tools for Digital Asset management.", "wallet-requirements": "<b>Wallet Requirements.</b> Users must have a compatible Wallet to utilize the Services.", "fees": "<b>Fees.</b> The Service Provider charges a transaction fee of 1% for \"buy\" or \"sell\" actions and provides notice of any changes to fee structures through official channels."}, "compliance": {"title": "Compliance and Disclaimers", "responsibilities": "<b>User Responsibilities.</b> Users must:", "responsibilities-1": "Comply with all Applicable Laws.", "responsibilities-2": "Ensure that all funds and Digital Assets used within the Services originate from lawful sources.", "responsibilities-3": "Refrain from engaging in prohibited activities, including market manipulation.", "disclaimer": "<b>Disclaimer of Warranties.</b> The Services are provided \"as is\" without any guarantees regarding availability, accuracy, or suitability."}, "dispute": {"title": "Dispute Resolution", "waiver": "<b>Class Action Waiver.</b> Users waive the right to participate in class action lawsuits against the Service Provider.", "notice": "<b>Notice of Dispute.</b> Disputes must be reported in writing within 30 days of occurrence, and the Parties will attempt to resolve disputes through negotiation before proceeding to arbitration.", "arbitration": "<b>Arbitration Procedures.</b> All disputes that cannot be resolved amicably shall be subject to binding arbitration conducted in Prague, Czech Republic, under Czech law."}, "termination": {"title": "Termination and Amendments", "termination": "<b>Termination.</b> The Service Provider may suspend or terminate access to the Services at its sole discretion.", "retention": "<b>Retention of Data.</b> The Service Provider may retain Transaction Data for a period required by Applicable Laws.", "amendments": "<b>Amendments.</b> The Service Provider reserves the right to modify these Terms at any time, with updates communicated through official channels."}, "additional-provisions": {"title": "Additional Provisions for Fatbot Users", "jurisdiction": "<b>Jurisdiction and Governing Law.</b> These Terms shall be governed by the laws of the Czech Republic.", "force": "<b>Force Maj<PERSON>e <PERSON>.</b> The Service Provider shall not be held responsible for delays or failures caused by circumstances beyond reasonable control, including natural disasters or legal constraints", "invalidity": "<b>Partial Invalidity.</b> Should any provision within these Terms be deemed invalid or unenforceable, all other sections will remain effective and enforceable.", "transfer": "<b>Transfer of Rights.</b> Users cannot transfer their rights under these Terms without prior consent from the Service Provider.", "communication": "<b>Communication Channels.</b> All official updates, support, and communications will be issued through www.fatty.io <NAME_EMAIL>."}}, "sign-in": {"welcome": "Welcome back to FATBOY", "email": "Email address", "password": "Password", "forgot": "Forgot your password?", "login": "<PERSON><PERSON>", "reset-password": "Reset it", "confirm": "Confirm", "totp-required": "TOTP is required.", "totp-wrong-format": "TOTP must be exactly 6 digits long.", "totp-placeholder": "6-Digit Code", "mfa-error": "Some unknown error occurred.", "sign-up": {"title": "Don't have an account?", "cta": "Sign up", "form": {"totp-reminder": "Enter the 6-digit code from your authenticator app to proceed.", "email-required": "Email is required", "password-required": "Password is required", "email-wrong-format": "Email is not in the correct format"}}}, "not-found": {"title": "Page not found", "back": "Go to homepage"}, "wallets": {"gas-fee": "Gas fee", "all": "All", "my-wallets": "My Wallets", "main": "Main", "import": "Import", "create": "Create new", "disconnect": "Disconnect", "delete-success": "Wallet deleted", "create-success": "Wallet created", "import-success": "Wallet imported", "private-key-copied": "Private key copied to clipboard", "address-copied": "Wallet address copied to clipboard", "create-wallet": "Create wallet", "choose-chain": "Choose <PERSON>", "create-wallet-name": "Create Wallet Name", "enter-private-key": "Enter Private Key", "import-wallet": "Import wallet", "wallet-name": "Wallet name", "private-key": "Private key", "maximum-wallets": "You can have a maximum of {count} wallets per chain.", "maximum-wallets-SOLANA": "You can have a maximum of {count} SOL wallets.", "maximum-wallets-EVM": "You can have a maximum of {count} EVM wallets.", "validations": {"import-wallet-name": "Please enter your wallet's name.", "import-wallet-private-key": "Please enter your private key.", "token": "Please select a token.", "address": "Please enter your address in the correct format.", "same-address": "You cannot withdraw to the same address.", "amount": "Please enter your amount.", "asset-amount-required": "Please select an asset amount before proceeding.", "insufficient-balance": "Insufficient balance.", "insufficient-balance-for-gas-fees": "Insufficient balance to cover gas fees."}, "errors": {"evm-import": "Could not import wallet.", "sol-import": "Could not import wallet."}, "toast": {"update-success": "Wallet successfully updated"}, "withdraw-failed": "<PERSON><PERSON><PERSON> failed", "no-results": "No results", "error-loading-tokens": "Failed to load tokens. Please try again later.", "amount-of-withdrawal": "Amount of Withdrawal", "youre-withdrawing": "You’re Withdrawing", "withdraw-to-address": "Withdraw to Address", "youve-successfully-withdrawn": "You’ve successfully withdrawn {amount} {token}.", "success-withdraw": "Success withdraw", "view-my-transactions": "View my transactions", "back-to-home": "Back to home", "no-wallets": "No wallets found", "evm-wallets": "Ethereum Wallets", "solana-wallets": "Solana Wallets", "total-profit": "Total Profit", "total-1d-change": "Total 1D Change", "show-private-key": "Show Private Key", "transactions": "Transactions", "my-tokens": "My Assets", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "choose-token": "Choose asset", "best-results": "Best Results", "token-name": "Token Name", "edit": "Edit", "save": "Save", "edit-wallet-name": "Edit Wallet Name", "disconnect-wallet": "Disconnect Wallet", "open-wallet-in-explorer": "Open Wallet in Blockchain Explorer", "disconnect-wallet-description": "Are you sure you want to disconnect your wallet? Your access to features linked to your wallet will be paused, but you can easily reconnect anytime.", "keep-it-secret": "Keep it Secret", "never-share-your-private-key": "Never share your private key. Anyone with access can control your wallet.", "enter-your-password": "Enter your password"}, "navigation": {"home": "Home", "manual-trading": "Manual Trading", "bot-trading": "Bot Trading", "overview": "Overview", "sign-out": "Sign out", "wallets": "Wallets", "bot-activity": "Bot Activity", "bot-settings": "<PERSON><PERSON>", "league": "League", "sniping-2-point-0": "Sniping 2.0", "fat-screener": "<PERSON> Screener", "beta": "Beta"}, "common": {"asset": "<PERSON><PERSON>", "no-results": "No Results", "automated-bots": "Automated Bots", "token": "Token", "price": "Price", "1h": "1H", "24h": "24H", "performance": "Performance", "1h-volume": "1H Volume", "24h-volume": "24H Volume", "all-wallets": "All wallets", "date": "Date", "wallet": "Wallet", "transaction-value": "Transaction Value", "transactions": "Transactions", "buy": "Buy", "coming-soon": "Coming Soon", "loading": "Loading...", "submit": "Submit", "presale": {"token-presale": "Fatty token presale", "token-presale-heading": "Get Priority", "token-presale-description": "The more $FATTY tokens you hold, the higher your priority. When demand spikes, token holders get faster transactions.", "token-presale-cta": "Buy $fatty tokens", "token-presale-read-more": "Read more"}, "learn-more": "Learn more", "total-loss": "Total Loss", "sell": "<PERSON>ll", "sent": "<PERSON><PERSON>", "fund": "Fund", "1h-volume-fat-bot": "24H Volume (Fat Bot)", "1h-volume-world": "1H Volume (World)", "something-went-wrong": "Something went wrong. Please try again later.", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "approve": "Approve", "search": "Search", "warning": "Warning", "continue": "Continue", "days": "Days", "error-loading-data": "Failed to load data. Please try again later.", "x-days": "{count} {count, plural, =1 {Day} other {Days}}"}, "onboarding": {"continue": "Continue", "step-1": {"title": "A Smarter Way to Trade Crypto", "description": "Take control of your trades or let our bots do the work. Choose your style, build your portfolio, and simplify your trading journey."}, "step-2": {"title": "Manual Trading Made Easy", "description": "Track tokens, monitor trends, and make real-time moves. Perfect for hands-on traders looking for precision."}, "step-3": {"title": "Automated Bots for 24/7 Trading", "description": "Activate bots to trade while you’re away. They find the best tokens, so you can grow your portfolio around the clock."}}, "deposit-to-wallet": {"title": "Deposit to wallet", "maximum-wallets": "You can have a maximum of {count} wallets.", "no-wallets": "No wallets found", "continue": "Continue"}, "setup-wallet": {"title": "Set Up Your Trading Wallet", "description": "To get started, import an existing wallet or create a new one. This wallet will be your secure gateway to trading and storing tokens.", "import-wallet": "Import wallet", "create-wallet": "Create wallet", "private-key-description": "Copy the Private Key and save it on secure place.", "wallet-name-placeholder": "Wallet Name", "private-key-placeholder": "Private Key", "finish": "Finish", "continue": "Continue", "done": {"title-create": "Success! Your Wallet Was Created!", "title-import": "Success! Your Wallet Was Imported!", "description": "You're all set! You can now start trading with your new wallet.", "copy-address": "Copy Address and Fill Up Your Wallet"}, "create": {"consent": "I have securely stored my private key", "show-private-key": "Show private key", "private-key-info": "You can always find your private key in My Wallet section of My Profile", "private-key-title": "Private Key", "private-key-alert": "Never share your private key. Anyone with access can control your wallet."}, "import": {"title": "Import Wallet"}, "deposit": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Scan the QR code with your crypto app <br></br> to add funds to your wallet.", "network": "Ethereum Mainnet", "EVM_MAINNET": "Ethereum", "EVM_BASE": "Base", "SOLANA": "Solana", "copy-address": "Or, copy the address below to transfer manually:"}, "validations": {"max-wallet-limit-hit": "You have reached the maximum number of wallets for this chain. Try another chain or remove one of your wallets to add a new one.", "invalid-private-key": "Enter a valid private key", "min-wallet-name-length": "Wallet name must contain at least {min} character(s)", "max-wallet-name-length": "Wallet name must not contain more than {max} character(s)", "failed-to-import-wallet": "Failed to import wallet", "failed-to-update-wallet-name": "Failed to update wallet name", "choose-a-chain": "Choose a chain", "chain-must-be-chosen": "Chain must be chosen"}}, "manual-trading": {"find-and-buy-tokens": "Find & Buy Tokens", "paste-token-address": "Paste Token Address", "manual-trading": "Manual Trading", "search-tokens": "Search", "non-tradable-token": "Note: Token is non tradable", "total-value-of-portfolio": "Total Value of Manually Traded Portfolio", "my-tokens": "My Assets", "last-transactions": "Last Transactions", "hot-tokens-right-now": "Hot Tokens Right Now", "hot-tokens-right-now-subtitle": "Quick Buy executes directly from main wallet on each chain", "leading-tokens": "Leading tokens by 24-hour-volume", "profit": "Profit", "loading-transactions": "Loading transactions", "no-transactions": "There are no transactions", "you-reached-the-end": "You've reached the end of the list", "pending-tx": "Pending Transaction", "success-tx": "Success Transaction", "failed-tx": "Failed Transaction", "loading-tokens": "Loading tokens", "buy-new-token": "Buy new token", "best-results": "Best Results", "fuel-your-portfolio-deposit": "Fuel Your Portfolio - Deposit Funds Now!", "fuel-your-portfolio-deposit-short": "Fuel Your Portfolio", "no-more-tokens-available": "No more tokens available", "no-tokens": "There are no tokens", "featured-tokens": {"fatbot-featured": "FatBot Featured", "zero-percent-fees": "0% Fees"}, "validations": {"invalid-address": "Enter a valid EVM or Solana address"}, "errors": {"loading-failed": "Failed to load data. Please try again later.", "no-data": "No data available at the moment.", "quick-buy-failed": "Insufficient funds for Quick buy. Please deposit your main wallet", "quick-buy-zero-validation": "Amount must be greater than 0", "quick-buy-set-error": "Something went wrong with setting your quick buy amount. Please try again."}, "deposit-wallet": {"title": "Deposit to Wallet", "description": "Select the wallet you want to deposit to.", "evm-wallets": "Ethereum Wallets", "continue": "Continue", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposit-wallet": "Deposit wallet", "all-wallets": "All wallets"}}, "bot-trading": {"coming-soon-title": "The Greatest Trading Bot <br></br> For Crypto", "bot-trading": "Bot Trading", "transactions": "{count, plural, =1 {Transaction} other {Transactions}}", "transaction": "Transaction", "sniping-2-0": "Sniping 2.0 is coming – Q2 2025! Customize your bot's sniping parameters and dominate the game on pump.fun. Configure your bot, boost speed, maximize profits and take control like never before!", "create-bot": {"copy-settings": {"title": "Copy & Share Your Settings", "description": "Copy and share your bot strategy on our community ", "copy": "Copy", "download": "Download", "copied-to-clipboard": "Copied to clipboard!", "failed-to-copy": "Failed to copy image"}, "sol-amount-required": "SOL amount is required", "fat-shield": "Fat Shield", "optional": {"title": "Optional", "description": "Congrats! Your bot is ready.\nNext steps are optional."}, "maximum-bots": "You can have up to {count} bots", "skip": "<PERSON><PERSON>", "continue": "Continue", "form-validate": {"required": "This field is required.", "min": "Minimum value is {min}", "max": "Maximum value is {max}", "must-be-number": "Must be a number", "must-be-positive": "Must be a number greater than zero.", "bot-name-max-length": "Bot name must not contain more than 20 characters"}, "app-header": {"exit": "Exit", "save-and-exit": "Save & Exit"}, "footer": {"get-started": "Get Started", "continue": "Continue"}, "bot-setting": {"title": "<PERSON><PERSON>"}, "bot-summary": {"title": "<PERSON><PERSON>", "description": "Check the summary of your bot settings.", "success-caption": "All set. Ready to Launch.", "warning-caption": "Finish bot settings to launch the bot", "basics": "Basics", "name": "Name", "strategy": "Strategy", "trade-amount": "Trade amount", "frequency": "Frequency", "profit-target": "Profit Target", "stop-loss": "Stop-loss", "targets": "Targets", "market-cap": "Market Cap", "liquidity": "Liquidity", "volume": "Volume", "volume-ratio": "Volume Ratio", "holder-range": "Holder Range", "whale-monitoring": "Whale Monitoring", "whale-monitoring-description": "3 wallets hold more than", "buy-count": "Buy Count", "sell-count": "<PERSON><PERSON>", "priority": {"title": "Get Priority", "read-more": "Read More", "description": "The more $FATTY tokens you hold, the higher your priority. When demand spikes, token holders get faster transactions.", "cta": "Buy $fatty tokens"}, "created-at": "<PERSON><PERSON> created", "delete-bot": "Delete Bot", "delete-bot-dialog": {"launched-bot-title": "Delete bot", "draft-title": "Discard bot draft", "draft-description": "Are you sure you want to delete this bot draft? This action cannot be undone.", "launched-bot-description": "Are you sure you want to delete this bot? This action cannot be undone.", "cancel": "Cancel", "confirm": "Confirm", "success": "<PERSON><PERSON> has been successfully deleted", "error": "Failed to delete bot", "tooltip": "If you wish to delete this bot you need to do following steps: <br></br><br></br>1. Deactivate the bot. <br></br>2. Close all active bot trades. <br></br>3. Wait for 10 minutes.  <br></br>4. Withdraw all the funds from the bot.  <br></br>5. Delete the bot."}, "bot-avatar": "<PERSON><PERSON> avatar", "transactions-ratio": "Transactions Ratio", "optional": "Optional", "wallet": "Wallet"}, "launch-bot": "Launch The Bot", "launch-bot-missing-required-fields": "Fill in all required fields to enable bot launch", "launch-bot-my-bots-undefined": "Could not fetch your bots. Please try to refresh the page.", "step-1": {"title": "Create Your\nTrading Bot", "sections": {"1": {"title": "Set Up Your Bot", "description": "Choose a name and avatar to make your bot unique."}, "2": {"title": "Customize\nYour Strategy", "description": "Define amount per trade, daily buy limit, profit target, and stop–loss."}, "3": {"title": "Define Your\nTrading Targets", "description": "Set market cap, liquidity, volume, holder range, volume proportion, transaction ratio."}}}, "step-2": {"caption": "Step 1", "title": "Let’s Build Your Bot’s Identity", "description": "Choose a name, an avatar, and connect your wallet to bring your trading bot to life. It only takes a minute."}, "step-3": {"title": "Choose <PERSON>", "description": "You’re just choosing a visual of the bot.\nIt won’t affect its performance.", "select": "Select", "next": "Next"}, "step-4": {"title": "Give <PERSON><PERSON> a Name", "form": {"bot-name": "Name of the bot"}}, "step-6": {"caption": "Step 2", "title": "Let’s Set Trading Parameters", "description": "Define your trading strategy by setting amount per trade, daily buy limit, profit target, and stop–loss. Let’s fine-tune your bot for smarter trades."}, "step-7": {"title": "Amount per Trade", "description": "Choose how much to trade each time.", "amount-too-small": "The amount is too low compared to other traders.", "amount-normal": "Matches what most traders do.", "amount-too-large": "The amount is too high compared to other traders."}, "step-8": {"title": "Set Daily Buy Limit", "description": "Set how many trades your bot makes daily.", "input-label": "Maximum Daily Buys", "validation": "Please enter a valid buy limit.", "validation-required": "Daily buy limit is required", "validation-max-buy-limit": "Buy limit must be less than or equal to {max}.", "amount-normal": "If you are a beginner or wish to test a new strategy, we recommend setting this value lower, such as 50 trades per day."}, "step-9": {"title": "Trade Profit Target", "description": "Set the profit percentage to auto-sell.", "input-label": "Profit Target", "validation": "Profit target must be a number greater than zero.", "validation-required": "Profit target is required", "amount-too-small": "The profit target is much lower than the average choice of most traders.", "amount-normal": "Most traders set it to this level.", "amount-too-large": "The profit target is much higher than what most traders aim for. It may be overly optimistic."}, "step-10": {"title": "Set Trade Stop-Loss", "description": "Choose when your bot sells each trade to limit losses.", "input-label": "Stop-Loss", "validation": "Stop-Loss boundary must be a number greater than zero.", "validation-required": "Stop-loss is required", "validation-max-stop-loss": "Stop-loss must be less than or equal to 100%.", "amount-too-small": "The stop-loss is set too low. It might sell your tokens too early.", "amount-normal": "A safe choice for most traders.", "amount-too-large": "The stop-loss is set too high. It could lead to bigger losses."}, "step-11": {"caption": "Step 3", "title": "Set Your Token Trade Filters", "description": "Set your bot’s trading filters with market cap, liquidity, volume, holder range, volume proportion, transaction ratio."}, "step-12": {"title": "Set Market Cap", "description": "Set the market cap to target promising tokens.", "input-start-label": "Minimum", "input-end-label": "Maximum", "annotation": "A safe choice for most traders.", "validation": "Please define a valid market cap range."}, "step-13": {"title": "Define Liquidity", "description": "Set the liquidity range for sniping tokens.", "input-start-label": "Minimum", "input-end-label": "Maximum", "annotation": "Most traders stick to this range.", "validation": "Please define a valid liquidity range."}, "step-14": {"title": "Choose Volume", "description": "Define the token's daily trading volume range.", "input-start-label": "Minimum", "input-end-label": "Maximum", "annotation": "Most traders aim for this range.", "validation": "Please define a valid volume range."}, "step-15": {"title": "Set Holder Range", "description": "Target tokens with holders.", "input-start-label": "Minimum", "input-end-label": "Maximum", "annotation": "This range is the most popular choice.", "validation": "Please define a valid holder range."}, "step-16": {"input-label": "{numOfWallet} wallets hold more than", "title": "Whale Monitoring", "description": "Limit large wallets to avoid risky tokens.", "annotation": "Common choice for most users"}, "step-17": {"title": "Volume Proportion", "description": "Set the maximum sell volume ratio for each token.", "input-start-label": "Buy volume", "input-end-label": "Max sell volume", "annotation": "Good choice among majority of traders", "validation": {"sell-higher-buy": "Sell volume can't exceed buy volume.", "decimal-number": "Only whole numbers are allowed.", "buy-outside-range": "Value must be between 50% and 99%.", "sell-outside-range": "Value must be between 1% and 50%."}}, "step-18": {"title": "Transactions Ratio", "description": "Set the minimum of sell transactions required compared to buy transactions.", "input-start-label": "Buy transactions", "input-end-label": "Min. sell transactions", "annotation": "Common choice for most users", "validation": {"decimal-number": "Only whole numbers are allowed.", "outside-range": "Value must be between 0% and 100%."}}, "advanced-settings": {"title": "Fat Shield", "description": "Add guardrails to protect your bot from risky tokens."}, "infinite-value-placeholder": "Unlimited"}, "all-my-bots-portfolio-value": "All My Bots Portfolio Value", "activity-of-all-my-bots": "Activity of All My Bots", "my-bots": "My Bots", "leaderboard": "Leaderboard", "create-new-bot": "Create New Bot", "total-profit": "Total Profit", "sum-of-buys-and-sells": "Sum of all buys and sells", "volume": "Volume", "in-x-days": "in {days} days", "in-last-x-days": "in last {days} days", "buy-transactions": "Buy Transactions", "buy-transaction": "Buy Transaction", "profitable-sales": "Profitable Sales", "profitable-sale": "Profitable Sale", "loss-sales": "Loss Sales", "loss-sale": "Loss Sale", "ready-to-get-started": "Ready to Get Started?", "create-your-first-bot": "Create your first bot and watch it work magic.", "bot-profile": "Bot Profile", "bot-withdraw": {"all": "All", "withdraw": "Withdraw", "from-portfolio": "From {name}'s Portfolio", "x-portfolio": "{bot} Portfolio", "withdraw-to-wallet": "Withdraw to Wallet", "gas-fee": "Gas fee", "withdraw-button": "Withdraw", "bot-wallet-name": "{name}'s Wallet", "choose-wallet": {"title": "<PERSON><PERSON>", "withdraw-to-address": "Withdraw to Address", "confirm-button": "Confirm"}, "choose-asset": {"title": "<PERSON><PERSON>", "best-results": "Best Results", "no-results": "No assets found matching your search."}, "success-dialog": {"title": "You’ve successfully withdrawn {amount} {token}.", "view-my-transactions": "View my transactions", "back-to-bot": "Back to bot"}, "validations": {"percent-exceed": "Percentage cannot exceed 100%.", "percent-required": "Percentage must be greater than 0%.", "invalid-address": "Invalid wallet address. Please check and try again.", "same-address": "You cannot withdraw to the same address."}}, "bot-launch-dialog": {"continue": "Continue", "launch-bot-step": {"message": "Launching <br></br>{name} Bot", "progress-labels": {"contract-checking": "contract checking"}, "success-message": "Success! Your Bot <br></br> Was Created!", "copy-address": "Copy Address and Fill Up Bot Wallet", "address-copied": "Bot address copied to clipboard"}, "deposit-bot-funds": {"message": "Depo<PERSON><PERSON> <br></br>(SOL)", "description": "Scan the QR code with your crypto app <br></br> to add funds to your bot.", "network": "Solana", "copy-address": "Or, copy the address below to transfer manually:"}}, "bot-transactions": {"title": "My Transactions", "no-transactions": "No transactions", "errors": {"loading-failed": "Failed to load data. Please try again later."}, "loading-transactions": "Loading transactions", "youve-reached-the-end": "You've reached the end of the list", "pending-tx": "Pending Transaction", "success-tx": "Success Transaction", "failed-tx": "Failed Transaction"}, "bot-portfolio": {"info-card": {"title": "Waiting for Bot Activity", "description": "The bot has sufficient funds and will make its first transactions soon."}, "bot-portfolio": "Bot Portfolio", "errors": {"loading-failed": "Failed to load data. Please try again later."}, "no-more-tokens-available": "No more tokens available", "loading-tokens": "Loading tokens"}, "bot-trades": {"active": "Active", "closed": "Closed", "bot-trades": "Bot Trades", "errors": {"loading-failed": "Failed to load data. Please try again later."}, "no-more-tokens-available": "No more tokens available", "loading-tokens": "Loading tokens", "no-bot-trades-found": "No bot trades found.", "market-cap": "Market Cap", "liquidity": "Liquidity", "volume": "Volume", "holders": "Holders", "vol-ratio": "Vol. Ratio", "tx-ratio": "Tx. Ratio", "chart-placeholder": "Chart Placeholder", "wallet": "Wallet", "transaction-value": "Transaction Value", "sell-token-now": "Sell this token now.", "no-bot-trade-found": "No bot trade found.", "sell-initiated": "<PERSON><PERSON> initiated successfully", "failed-to-sell-token": "Failed to sell token", "cant-find-the-chart": "Can't find the chart?", "its-only-available-for-24-hours": "It’s only available for 24 hours after buying the asset.", "sell-reason": {"text": "<PERSON><PERSON>", "stop-loss-hit": "Stop Loss", "take-profit-hit": "Take Profit", "manual-sell": "<PERSON> Sell", "protection-rule-trigger": "Advanced Protection Rule"}}, "compare": "Compare", "in-one-day": "in 1 day", "buys": "{count, plural, =1 {1 buy} other {# buys}}", "sells": "{count, plural, =1 {1 sell} other {# sells}}", "complete-percentage": "{percentage}% complete", "complete-your-bot-creation-now": "Complete your bot creation now", "insufficient-funds": "Insufficient funds", "insufficient-funds-description": "Deposit Solana to activate the bot.", "insufficient-funds-cta": "Deposit to activate bot", "missing-bot-id": "Missing bot ID", "deposit-solana": "<PERSON><PERSON><PERSON><PERSON>", "upcoming-weeks": "Upcoming Weeks", "upcoming-months": "Upcoming Months", "live": "Live", "features": {"eth-network": "ETH Network", "anti-mev-protection": "Anti-MEV Protection", "honeypot-detection": "Honeypot Detection", "buy-sell-tax-protection": "Buy/Sell Tax Protection", "referral-system": "Referral System", "two-factor-authentication": "2FA Authentication", "advanced-charts": "Advanced Charts", "advanced-ai-security-audits": "Advanced AI Security Audits", "loyalty-fatty-power-system": "Loyalty Fatty Power System", "fatty-league": "Fatty League", "solana-network": "SOLANA Network", "base-network": "BASE Network", "bsc-network": "BSC Network", "arb-network": "ARB Network", "dca-bots": "DCA bots", "limit-orders": "Limit Orders", "sniping-2-0": "Sniping 2.0", "ai-agents": "AI Agents", "strategy-copy-trading": "Strategy Copy Trading", "block-zero-sniping": "Block Zero Sniping", "btc-network": "BTC Network", "fiat-onramp": "Fiat Onramp", "frontrunning": "Frontrunning", "february-2025": "February 2025", "march-2025": "March 2025", "april-2025": "April 2025", "may-2025": "May 2025", "june-2025": "June 2025", "not-opensource-sc-detection": "Not-OpenSource SC Detection", "usdc-support": "USDC Support", "zero-fees": "0% Fees", "penetration-tests": "Penetration Tests", "50-percent-speed-increase": "50% Speed Increase", "fatbot-development": "#FatBotDevelopment", "multi-dex-support": "Multi-DEX Support", "pump-fun-support": "Pump.Fun Support", "smooth-ux-and-ui": "Smooth UX and UI", "new-website": "New Website", "whitepaper-update": "Whitepaper UPDATE Included", "sniping-2-0-available-for-public": "Sniping 2.0", "available-for-public": "Available For Public", "custom-s2-0-security-features": "Custom S2.0 Security Features", "advanced-limit-orders": "Advanced Limit Orders", "sniping-2-0-ai-advisory": "Sniping 2.0 AI ADVISORY", "fatty-pre-tge-airdrop": "Fatty Pre-TGE Airdrop", "video-update-soon": "Video update soon", "fatty-league-hook-model": "Fatty League/Hook Model", "fiat-onramp-via-transak": "Fiat Onramp", "via-transak": "via TRANSAK", "fatscreener-full-scale-blockchain-filter": "FatScreener", "full-scale-blockchain-filter": "Full Scale Blockchain Filter", "3rd-party-audits": "3rd Party audits", "springboard-pancakeswap": "SpringBoard.Pancakeswap", "for-sniping-2-0-on-bsc": "For Sniping 2.0 on BSC", "cross-chain-build-in-bridge": "Cross-chain Build-in Bridge", "for-smooth-cross-chain-transactions": "For Smooth cross-chain Transactions", "24-7-support": "24/7 Support", "main-volume-airdrop-campaigns": "Main Volume Airdrop Campaigns", "fatbot-app": "FatBot App", "app-store-google-play": "App Store + Google Play"}, "bot-compare": {"bot-portfolio-value": "{name} Portfolio Value", "bot-activity": "Bot Activity", "bot-portfolio-allocation": "Bot Portfolio Allocation", "compare-bots": "Compare Bots", "my-transactions": "My Transactions", "no-bots-to-compare": "No bots to compare", "no-portfolio-data": "No portfolio data available. Check back later for updates!", "bot-status-updated": "Bot status updated successfully", "bot-status-update-failed": "Failed to update bot status", "toggle-to-deactivate-bot": "Toggle to deactivate the bot", "toggle-to-activate-bot": "Toggle to activate the bot", "no-bot-transactions-found": "No bot transactions found.", "no-portfolio-allocation-data": "No portfolio allocation data available. Check back later for updates.", "summary": {"bot-settings": "<PERSON><PERSON>", "caption": "Check the summary of your bot settings", "basics": "Basics", "strategy": "Strategy"}, "settings": {"title": "Compare Bots", "basics": "Basics", "strategy": "Strategy", "targets": "Targets", "optional": "Optional", "links": {"name": "Name", "wallet": "Wallet", "trade-amount": "Trade amount", "frequency": "Frequency", "profit-target": "Profit Target", "stop-loss": "Stop-loss", "market-cap": "Market Cap", "liquidity": "Liquidity", "volume": "Volume", "holder-range": "Holder Range", "volume-ratio": "Volume Ratio", "whale-monitoring": "Whale Monitoring", "whale-monitoring-info": "{amount} walltes hold more than", "buy-count": "Buy Count", "sell-count": "<PERSON><PERSON>"}, "bot-created": "<PERSON><PERSON> created {date}", "delete-bot": "Delete Bot"}}, "bot-toggle": {"bot-status-updated": "Bot status updated successfully", "bot-status-update-failed": "Failed to update bot status", "enable": {"title": "Enable <PERSON>", "prompt": "Are you sure you want to active this bot?", "cta": "Activate"}, "disable": {"title": "Disable Bo<PERSON>", "prompt": "Are you sure you want to deactive this bot?", "cta": "Deactivate"}}, "bot-detail": {"bot-active": "Bo<PERSON> active", "bot-not-active": "<PERSON><PERSON> not active", "bot-portfolio-value": "{botName} Portfolio Value", "my-transactions": "My Transactions", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "bot-activity": {"title": "Bot Activity", "buy-transactions": "Buy Transactions", "profit-sales": "Profitable Sales", "loss-sales": "Loss Sales", "volume": {"title": "Volume", "description": "Sum of all buys and sells"}}, "pause-bot-dialog": {"title": "Withdrawing? We’ll Pause the Bot First.", "text": "To keep things safe, your bot will be paused automatically. You can always turn it back on.", "pause-and-withdraw": "Pause & Withdraw"}, "Insufficient": "Insufficient Funds.", "buys": "Buys", "buy": "Buy", "left": "left", "no-bot-detail-settings-found": "No bot detail settings found", "main-wallet": "Main Wallet"}, "bot-leaderboard": {"info-caption": "Strategy copy trading coming soon!", "bot-league": "Bot League", "all": "All", "my-bots": "My Bots", "see-all": "See all", "profit-percentage": "Profit (%)", "profit-usd": "Profit (USD)", "wallet-balance": "<PERSON><PERSON>", "no-leaderboard-data": "No leaderboard data available. Check back later for updates!", "coming-soon": "COMING SOON", "days-left-to-launch": "{time} left to launch"}, "select-active-bots": {"title": "Select Your Active Bots", "caption": {"title": "You’ve exceeded the bot limit", "description": "Only {count} bots can be active at a time."}, "continue": "Continue", "bot-active": "Bo<PERSON> active", "bot-deactivated": "Bot deactivated", "clone": "<PERSON><PERSON>"}, "deposit": {"title": "Deposit to trading bot"}, "advanced-trading-settings": {"title": "Advanced Trading Settings", "bot-settings-updated": "Bot settings updated", "new-wallet-activity-filter": "New Wallet Activity Filter", "new-wallet-activity-filter-tooltip": "Prevents trading tokens heavily targeted by newly created wallets with low on-chain activity.", "wait-before-buying": "Wait Before Buying", "wait-before-buying-tooltip": "Bots wait a few minutes after a token is launched to reduce the risk of early scam tokens.", "creator-wallet-limit-guard": "Creator Wallet Limit Guard", "creator-wallet-limit-guard-tooltip": "Stops trading bot from buying tokens   if the creator wallet holds too much at the time of launch.", "suspicious-bundle-detection": "Suspicious Bundle Detection", "suspicious-bundle-detection-tooltip": "Automatically sells and blacklists tokens if many wallets buy in the same block.", "auto-sell-after-hold-time": "Auto-Sell After Hold Time", "auto-sell-after-hold-time-tooltip": "Automatically exits positions after a few minutes to secure gains and reduce exposure to sudden drops.", "duplicate-ticker-protection": "Duplicate Ticker Protection", "duplicate-ticker-protection-tooltip": "Prevents bots from buying newly created tokens with the same name as a token created recently.", "update-failed": "Failed to update bot settings", "single-high-buy-detection": "Oversized single buy detection", "single-high-buy-detection-tooltip": "Bots automatically blacklist tokens with suspiciously large single purchases, which can potentially lead to a rug-pull scenario."}, "bot-status": {"sell-manually": "Sell manually", "scanning-for-opportunities": "Scanning for Opportunities", "your-bot-is-active": "Your bot is active but hasn't found tokens matching your criteria yet.", "your-bot-is-scanning-and-trading-tokens": "Your bot is scanning and trading tokens based on your strategy.", "daily-limit-reached": "Daily Limit Reached", "your-bot-has-reached-its-limit": "Your bot reached the daily trade limit. Reset or adjust strategy.", "insufficient-balance": "Insufficient Balance", "deposit-more-funds-to-activate-your-bot-and-start-trading": "Deposit more funds to activate your bot and start trading.", "bot-deactivated": "Bot Deactivated", "activate-your-bot-to-start-scanning": "Activate your bot to start scanning and trading tokens.", "funds-in-open-positions": "All funds are in trades. Wait, deposit, or sell to resume.", "adjust-settings": "Adjust settings", "reset": "Reset", "adjust": "Adjust", "deposit-funds": "Deposit funds", "activate-bot": "Activate bot", "renew-trades-tooltip": "To renew buys count activate the bot"}, "unlock-beta-dialog": {"title": "Unlock beta access to Sniping 2.0", "description": "Just ${amount} more to reach ${target} in trade volume and unlock beta bot access.", "your-lifetime-volume": "Your Lifetime Volume", "volume-of-target": "${current} of ${target}", "beta-testers-update": "The list of Beta Testers is updated every day at 12:00 UTC", "trade-now": "Trade now", "fatty-beta-unlock": "<PERSON>ty <PERSON>", "bot-trading-locked": "Bot Trading Locked"}}, "period-change": {"1h": "1H Change", "1d": "1D Change", "7d": "7D Change", "30d": "30D Change", "total-period-change": "Total {period} Change"}, "lists": {"see-all": "See all"}, "profile": {"logged-as": "Logged as", "my-account": "My Account", "personal-details": "Personal Details", "my-wallets": "My Wallets", "wallets-connected": "{count} connected", "referral": "Referral", "community": "Community", "telegram": "Telegram", "discord": "Discord", "support": "Support", "white-paper": "White Paper", "leave-feedback": "Leave Feedback", "faqs": "FAQs", "legal": "Legal", "terms-and-conditions": "Terms and Conditions", "privacy-policy": "Privacy Policy", "log-out": "Log Out", "delete-account": "Delete Account", "web-app-version": "Web App Version {version}", "settings": {"title": "Personal Details", "email-address": "Email address", "need-to-change": "Need to change password?", "reset-it": "Reset it", "cta": "Cancel"}, "setup-mfa": "Two-Factor Autentication", "lifetime-volume": "Lifetime Volume", "referral-rewards-unlocked": "Referral Rewards Unlocked", "you-are-enjoying-active-benefits": "You're enjoying active benefits from being invited."}, "referral": {"heading": "Earn 30% Fees,<br></br> Share Discounts", "caption": "Invite friends with your referral link, earn 30% of their trading fees. They'll get 10% off manual trades - a win-win for every one!", "total-earnings": "Total earnings", "referrals": "{count} Referrals", "claimable-earnings": "Claimable earnings", "invite-friends": "Invite friends", "claim-rewards": "Claim rewards", "claim-error": "There was an error claiming your rewards. Please try again.", "disabled-claim-tooltip": "The minimum claim amount is 0.05 ETH", "minimum-claimable-amount": "Minimum claim is {amount} {chain}", "claim-referral-dialog": {"title": "You've successfully <br></br>claimed<br></br>{amount}.", "view-my-tokens": "View my tokens", "back-to-referral": "Back to referral"}}, "invite": {"heading": "Invite Friends", "description": "Share your referral link or QR code to start earning 30% of their trading fees. Your friends will save with exclusive discounts on trading!", "copy-referral-link": "Copy your referral link", "share": "Share", "link-copied": "Link copied!", "click-to-copy": "Click to copy", "create-invite-link": "Create In<PERSON>te <PERSON>", "warning": "It can be created only once. Can't be edited later.", "invite-link": "Invite Link", "cta": "Save", "validation-messages": {"required": "Referral code is required", "min-length": "Referral code must be at least 4 characters.", "max-length": "Referral code must not be longer than 20 characters.", "format": "Referral code can only letters and digits.", "already-exists": "The chosen referral code is already being used."}}, "token-detail": {"buys": "{count, plural, =1 {1 Buy} other {# Buys}} (last 24h)", "sells": "{count, plural, =1 {1 Sell} other {# Sells}} (last 24h)", "wallet-balance": "Your wallet balance is", "spending": "You're spending", "earning": "You're earning", "selling-token": "You're selling", "buying-token": "You're buying", "price": "Price", "liquidity": "Liquidity", "wallet": "Wallet", "performance": "Performance", "market-cap": "Market Cap", "volume": "Volume", "created-on": "Created on", "stats": "Stats", "FDV": "FDV", "1-d-volume": "1 Day Volume", "my-transactions": "My Transactions", "my-holdings": "My Holdings", "token-info": "Token Info", "token-address": "Token Address", "preset-all": "All", "buy": "Buy", "sell": "<PERSON>ll", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "order-type-tabs": {"market": "Market", "limit": "Limit"}, "timeframe": {"1H": "1H", "1D": "1D", "1W": "1W", "1M": "1M", "1Y": "1Y", "ALL": "ALL"}, "trading": {"trade-failed": "Trade failed", "view-my-assets": "View my assets", "back-to-market": "Back to market", "token-required": "Token amount is required", "chain-amount-required": "{chain} amount is required", "wallet-required": "Wallet is not selected", "insufficient-balance": "Insufficient balance.", "max-buy-error-message": "The maximum buy is {amount} ETH.", "progress": {"contract-checking": "Contract checking", "honeypot-checking": "Honeypot checking", "anti-mev-initiated": "Anti-MEV initiated", "frontrunning-protected": "Frontrunning protected", "finalizing-transaction": "Finalizing transaction", "confirmation": "Confirmation"}, "transaction-taking-longer": "This transaction is taking longer than usual. You can safely leave, and it will be processed as soon as possible.", "success-message-buy": "You’ve successfully purchased {amount} {tokenSymbol}.", "success-message-sell": "You’ve successfully sold {amount} {tokenSymbol}.", "securing-your-purchase": "Securing Your Purchase", "securing-your-sale": "Securing Your Sale"}, "advanced-settings": {"gas-fee": "Gas fee", "gas-fee-unavailable": "Gas fee is unavailable because transaction would most likely fail", "advanced-details": "Advanced Details", "network-cost": "Max Network Cost", "network-cost-tooltip": "This is the cost to process your transaction on the blockchain. Fatbot does not receive any share of these fees.", "anti-mev-check": "Anti-MEV Check", "anti-mev-check-tooltip": "Protects trades from front-running attacks.", "anti-mev-active": "Active", "anti-mev-inactive": "Inactive", "anti-mev-check-enabled": "Enabling MEV protection may slow down the execution of the trade.", "anti-mev-check-disabled": "Anti-MEV Check setting updated.", "anti-mev-check-error": "Unable to update Anti-MEV Check setting.", "import-tokens": "Import Tokens", "import-tokens-tooltip": "Adds a token or additional token balance to your wallet.", "import": "Import", "buy-tax": "Buy Tax", "sell-tax": "Sell Tax", "auto": "Auto", "dex": "DEX", "dex-tooltip": "Decentralized exchange is a blockchain-based platform that allows users to trade cryptocurrencies.", "success-message-import": "You’ve successfully imported {tokenSymbol}.", "error-message-import": "You’ve failed to import {tokenSymbol}.", "transfer-fee": "Transfer Fee", "trade-tax-fee-tooltip": "This is the tax charged by the token itself and goes to the token creator. Fatbot does not receive any share of these fees."}, "scam-warning": {"title": "Smart Contract Not Verified", "description": "This token's smart contract is not open-source. Its code cannot be reviewed and may hide security risks.", "continue": "Continue anyway", "go-back": "Go back safely"}, "potential-honeypot-dialog": {"title": "Potential Honeypot <br></br> or other Risk Detected!", "description": "This token may act as a honeypot or involve other high-risk behaviors such as restricted selling or extreme price impact. \nProceed with caution.", "continue": "CONTINUE ANYWAY", "go-back": "Go back safely"}, "buy-warning": {"title": "Warning", "possible-honeypot": "Possible honeypot or other risk detected. Selling may be restricted or result in significant losses.", "possible-scam": "This token's smart contract is not open-source. Its code cannot be reviewed and may hide security risks."}, "not-tradeable": {"title": "This token is currently not tradeable. Please try again later.", "back": "Go back"}, "ai-audit": {"audit": "Audit", "title": "AI Audit", "fat-auditor": "Fat Auditor", "danger": "Danger", "caution": "Caution", "safe": "Safe", "checking": "Checking", "this-is-generated-by-ai": "This is generated by AI from the token's smart contract. Always do your own research.", "potential-safety-issue-detected": "Potential safety issue detected.", "safety-issue-detected": "Safety issue detected.", "see-details": "See Details"}}, "chat": {"support-community": "Support & Community", "need-help": "Need help? Discuss strategies or get 24/7 support.", "telegram": "Telegram", "discord": "Discord", "report-bug": "Report a bug", "fatty-head": "<PERSON><PERSON>"}, "home-page": {"portfolio": "Portfolio", "asset-allocation": {"title": "Asset allocation", "manually-traded": "Manually Traded", "manually-traded-tokens": "Manually Traded Tokens", "bot-traded-tokens": "Automated Bots", "awaits-allocation": "Awaits allocation", "my-wallets": "My Wallets", "see-all": "See all", "set-up-wallet": "Set Up Your Trading Wallet", "set-up-wallet-description": "To get started, import an existing wallet or create a new one. This wallet will be your secure gateway to trading and storing tokens.", "set-up-wallet-cta": "Set up wallet"}, "my-top-bots": "My top bots", "total-portfolio-value": "Total portfolio value", "total-profit": "Total profit", "total-1d-change": "Total 1D Change", "referral": "Referral", "earn-rewards": "Earn rewards!", "get-30-of-friends-fees": "Get 30% of your friends' trading fees and help them save with discounts.", "invite-friends": "Invite friends", "sniping-2-0-coming": "Sniping 2.0 is coming! Fine-tune your bot and boost speed with Fatty tokens. Stay ahead!", "coming-soon": "Coming Soon", "my-top-performing": "My Top Performing", "fuel-your-portfolio": "Fuel Your Portfolio", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "error-loading-data": "Failed to load data. Please try again later.", "total-loss": "Total Loss"}, "bot-dex-selector": {"springboard-soon": "Springboard soon", "pump-fun": "Pump.fun", "all": "ALL"}, "relative-dates": {"just-now": "just now", "aboutXMinutes": "{count, plural, one {# minute ago} other {# minutes ago}}", "aboutXHours": "{count, plural, one {# hour ago} other {# hours ago}}", "aboutXDays": "{count, plural, one {# day ago} other {# days ago}}", "xMinutes": "{count, plural, one {# minute ago} other {# minutes ago}}", "xHours": "{count, plural, one {# hour ago} other {# hours ago}}", "xDays": "{count, plural, one {# day ago} other {# days ago}}", "aboutXSeconds": "{count, plural, one {# second ago} other {# seconds ago}}", "xSeconds": "{count, plural, one {# second ago} other {# seconds ago}}", "aboutXWeeks": "{count, plural, one {# week ago} other {# weeks ago}}", "xWeeks": "{count, plural, one {# week ago} other {# weeks ago}}", "aboutXMonths": "{count, plural, one {# month ago} other {# months ago}}", "xMonths": "{count, plural, one {# month ago} other {# months ago}}", "aboutXYears": "{count, plural, one {# year ago} other {# years ago}}", "xYears": "{count, plural, one {# year ago} other {# years ago}}", "overXYears": "{count, plural, one {over 1 year ago} other {over # years ago}}", "almostXYears": "{count, plural, one {almost 1 year ago} other {almost # years ago}}", "lessThanXSeconds": "less than a second ago", "lessThanXMinutes": "less than a minute ago", "halfAMinute": "half a minute ago", "xAgo": "{time} ago"}, "duration-format": {"lessThanXSeconds": "{count}s", "xSeconds": "{count}s", "halfAMinute": "30s", "lessThanXMinutes": "{count}m", "xMinutes": "{count}m", "aboutXMinutes": "{count}m", "aboutXHours": "{count}h", "xHours": "{count}h", "xDays": "{count}d", "aboutXDays": "{count}d", "xWeeks": "{count}w", "aboutXWeeks": "{count}w", "aboutXMonths": "{count}mo", "xMonths": "{count}mo", "aboutXYears": "{count}y", "xYears": "{count}y", "overXYears": "{count}y", "almostXYears": "{count}y"}, "maintenance": {"title": "Maintenance", "description": "Oops! Our servers are taking a coffee break ☕️ We'll be back faster than you can say 'crypto-fatbot-caffeine'! Check back soon!", "reload": "Reload"}, "coming-soon": {"title": "Coming Soon", "description": "Unlock your potential with the greatest trading bot for crypto, designed to maximize your profits while you sleep!", "video-dialog-title": "Fatty trailer"}, "time-range-labels": {"hour": "hour", "day": "day", "week": "week", "month": "month", "year": "year", "all": "all"}, "in-x-days": {"hour": "in 1 hour", "day": "in 1 day", "week": "in 7 days", "month": "in 1 month", "year": "in 1 year", "all": "all time"}, "streak": {"x-day-streak": "🔥 {count}-Day Streak", "day-streak": "day streak", "current-day-streak": "current day streak", "days-left-to-multiplier": "Just {count} days left to earn a {multiplier}X 🍩 Multiplier!", "streak-description": "Trade daily to keep your streak and earn rewards—longer streaks mean bigger rewards. Miss a day, and it resets!", "continue": "Continue", "streak-multiplier": "Streak Multiplier", "donut-multiplier": "🍩 Multiplier", "you-earned-x-multiplier": "You earned a {multiplier}x 🍩 Multiplier for staying consistent. Keep trading daily to stack even bigger rewards!", "claim-donut-multiplier": "Claim 🍩 MULTIPLIER", "streak-reward-unlocked": "Streak Reward Unlocked!", "active-now": "Active Now", "stays-until-streak-breaks": "Stays Until Streak Breaks", "streak-loose-title": "You're about to lose your {count}-day streak", "streak-loose-description": "Keep your streak alive by trading today.", "streak-loose-time-left": "You have {time} left to save your {multiplier}x 🍩 Multiplier", "streak-loose-info": "Trade daily to keep your streak and earn rewards—longer streaks mean bigger rewards. Miss a day, and it resets!", "streak-loose-trade-now": "Trade Now", "just-x-more-days-to-unlock-next-reward": "Just {count} more {count, plural, one {day} other {days}} to unlock next reward", "lost-streak-title": "You lost your streak! But you can start again today!", "your-multiplier-is-gone": "Your {multiplier}x 🍩 Multiplier is gone—earn it back!", "your-progress-is-not-lost-just-delayed": "Your progress isn't lost—just delayed! <br></br>Trade today to start fresh, rebuild your streak, and keep earning bigger rewards!", "get-back-on-track": "Get back on track", "lost-streak": "lost streak", "streak-loose": "streak loose", "streak-strategy-title": "🔥 Streak Strategy", "trade-daily-to-grow-your-streak": "Trade daily to grow your streak and earn bigger multipliers. Miss a day, and your streak resets—losing your boost!", "at-risk": "At Risk", "trade-now-to-save-your-multiplier": "{timeLeft} left! Trade now to save your multiplier!", "streak-rewards": "Streak Rewards", "stay-consistent-stack-more-earn-more": "Stay consistent. Stack more. Earn more.", "more-info": "More Info", "trade-now": "Trade Now"}, "rewards": {"show-reward": "Show reward", "fatty-card-ready": "Your fatty card is ready!", "whats-inside": "What's inside? A rare boost? a jackpot?", "reveal-prize": "Reveal prize", "reward-claimed": "<PERSON><PERSON> claimed"}, "reward-variants": {"happy-fatty-alt": "<PERSON> Fatty", "claim": "Claim 🍩", "common": {"badge": "Lucky Find!", "caption": "A nice surprise!", "text": "Keep trading—your next card could be a jackpot!"}, "uncommon": {"badge": "Rare Find!", "caption": "Not bad! Keep going!", "text": "Keep trading—your next card could be a jackpot!"}, "rare": {"badge": "Major Find!", "caption": "A solid win!", "text": "Keep trading—your next card could be a jackpot!"}, "legendary": {"badge": "Legendary Find!", "caption": "Now we're talking!", "text": "Keep trading—your next card could be a jackpot!"}, "mythical": {"badge": "Mythical Find!", "caption": "This is HUGE!", "text": "Keep trading—your next card could be a jackpot!"}, "jackpot": {"badge": "YOU HIT THE JACKPOT!", "caption": "Unbelievable!", "text": "A once-in-a-lifetime score! Luck like this doesn't come twice — savor the moment!"}}, "weekdays": {"short": {"mo": "Mo", "tu": "Tu", "we": "We", "th": "Th", "fr": "Fr", "sa": "Sa", "su": "Su"}, "x-days": "{count, plural, one {# day} other {# days}}", "x-hours": "{count, plural, one {# hour} other {# hours}}"}, "fatty-league": {"multiplier": "{multiply}X 🍩 MULTIPLIER", "league-leaderboard": {"header": "Weekly", "title": "Fatty League", "text": "Climb the ranks, stack 🍩, and earn more $FATTY—faster transactions, bigger profits!"}, "league-leaderboard-detail": {"title": "Fatty League", "league-info": {"reset-in": "Weekly Fatty League Resets in: ", "climb-the-ranks": "Climb the Ranks & Maximize Profits", "step-1": {"title": "Trade & Earn 🍩 Donuts", "text": "Every $1 traded = 🍩 1 Donut. <br></br> More volume, more 🍩 Donuts!"}, "step-2": {"title": "Rank Up & Multiply Your 🍩 Donuts", "text": "The higher your rank, the bigger your real-time 🍩 Donut multiplier!"}, "step-3": {"title": "Stack 🍩 Donuts & Secure Rank", "text": "Your total 🍩 Donuts each week determine your final monthly rank."}, "step-4": {"title": "Monthly Rank → $FATTY Rewards", "text": "Your monthly rank in Fatty League decides your share of $FATTY."}, "step-5": {"title": "More $FATTY = More Trading Power", "text": "Holding $FATTY gives you faster transactions & bigger profits!"}}, "league-rewards": {"title": "Fatty League Rank Rewards", "rank-reward": "Top {rank}", "rewards": {"reward-1": {"icon": "🏆", "multiplier": "2"}, "reward-2": {"icon": "🔥", "multiplier": "1.75"}, "reward-3": {"icon": "🚀", "multiplier": "1.5"}, "reward-4": {"icon": "💎", "multiplier": "1.25"}, "reward-5": {"icon": "⚡️", "multiplier": "1.1"}}, "more-info": "More info", "trade-now": "Trade now"}, "rank-card": {"volume": "Volume {volume}"}}, "claim-reward-dialog": {"header-icon": "🎉", "header-text": "Weekly Fatty League Reset!", "title": "You Finished <br></br> at Rank #{rank}", "description": "You Made It Into the Top {topRank}!", "summary": "Total 🍩 count this month: {totalCount} 🍩. <br></br> Keep stacking—every week counts! Your final  🍩 count will determine your share of $FATTY.", "claim-reward": "<PERSON><PERSON><PERSON>"}, "multiplier-dialog": {"title": "This is Your Total <br></br> 🍩 Multiplier", "text": "You always see just one combined multiplier, but it’s made of these two!", "continue": "Continue"}, "exchange-donuts-dialog": {"header-icon": "🎉", "header-text": "Monthly Fatty League Complete!", "title": "You Stacked Up 🍩 {donuts} Donuts", "summary": "Your hustle paid off—every trade, every multiplier, every move you made led to this moment.", "exchange": "Exchange donuts for $FATTY"}, "earned-fatty-tokens": {"title": "You Earned", "period": "this {period}"}, "multipliers": {"fatty-league": {"name": "Fatty League", "text": "Live, based on your Fatty League postition"}, "streak": {"name": "Streak", "text": "Active, until streak is broken"}}}}